/**
 * Game Controller
 * Handles game creation, state management, and player actions
 */

const { v4: uuidv4 } = require('uuid');
const Game = require('../../../game_logic/Game');
const GameSession = require('../../models/multiplayer/GameSession');
const GameMove = require('../../models/multiplayer/GameMove');
const MatchmakingQueue = require('../../models/multiplayer/MatchmakingQueue');

// Costante per elementi di gioco
const VERTEX_POSITIONS = ['a1', 'f1', 'a6', 'f6'];

// In-memory storage for active games
const activeGames = {};
// Map from socket ID to game ID
const playerGameMap = {};
// Map from game ID to turn timeout
const gameTurnTimeouts = {};
// Map to keep track of disconnected players (userId => {gameId, socketId, disconnectedAt})
const disconnectedPlayers = {};
// Map from userId to previous socketId (for reconnection)
const userIdToSocketId = {};

// Constants
const DEFAULT_TURN_TIMEOUT = 60; // seconds
const RECONNECTION_GRACE_PERIOD = 30; // seconds to allow for reconnection

class GameController {
    constructor() {
        this.io = null;
    }

    /**
     * Initialize the game controller with Socket.IO instance
     * @param {SocketIO} io - Socket.IO instance
     */
    initialize(io) {
        this.io = io;
        console.log('[GAME CONTROLLER] Initialized with Socket.IO instance');
    }

    /**
     * Handle player reconnection
     * @param {string} userId - User ID
     * @param {string} newSocketId - New socket ID
     * @returns {Object|null} - Game state or null if reconnection failed
     */
    handlePlayerReconnection(userId, newSocketId) {
        // Check if player was disconnected
        if (!disconnectedPlayers[userId]) {
            console.log(`No disconnected player found for userId ${userId}`);
            return null;
        }
        
        const { gameId, socketId: oldSocketId, disconnectedAt, color, playerName } = disconnectedPlayers[userId];
        const disconnectionDuration = (Date.now() - disconnectedAt) / 1000; // in seconds
        
        // Check if game still exists
        const game = activeGames[gameId];
        if (!game) {
            console.log(`Game ${gameId} no longer exists for reconnection of userId ${userId}`);
            delete disconnectedPlayers[userId];
            delete userIdToSocketId[userId];
            return null;
        }
        
        // Check if game is already over
        if (game.gameOver) {
            console.log(`Game ${gameId} is already over, cannot reconnect userId ${userId}`);
            delete disconnectedPlayers[userId];
            delete userIdToSocketId[userId];
            return { event: 'reconnectionFailed', reason: 'gameOver' };
        }
        
        // Check if reconnection is within grace period
        if (disconnectionDuration > RECONNECTION_GRACE_PERIOD) {
            console.log(`Grace period expired for userId ${userId} in game ${gameId} (${disconnectionDuration.toFixed(2)}s)`);
            
            // Clean up disconnected player data
            delete disconnectedPlayers[userId];
            delete userIdToSocketId[userId];
            
            return { event: 'reconnectionFailed', reason: 'gracePeriodExpired' };
        }
        
        console.log(`Reconnecting userId ${userId} to game ${gameId} (was disconnected for ${disconnectionDuration.toFixed(2)}s)`);
        
        // Update player in game with new socket ID
        // First, we need to remove the old player and add them back with the new socket ID
        
        // Find the old socket ID for this user
        let oldPlayerSocketId = null;
        for (const playerId in game.players) {
            if (game.players[playerId].userId === userId) {
                oldPlayerSocketId = playerId;
                break;
            }
        }
        
        if (!oldPlayerSocketId) {
            console.log(`Could not find old player entry for userId ${userId} in game ${gameId}`);
            return { event: 'reconnectionFailed', reason: 'playerNotFound' };
        }
        
        // Store the player's current state before removing
        const playerData = game.players[oldPlayerSocketId];
        
        // ⚠️ CORREZIONE CRITICA: NON ripristinare la mano durante riconnessione
        // La mano potrebbe essere obsoleta se il giocatore ha giocato carte prima della disconnessione
        // Manteniamo invece lo stato attuale del gioco che è sempre aggiornato
        
        const playerScore = playerData ? playerData.score : 0;
        const wasCurrentPlayer = game.getCurrentPlayer()?.id === oldPlayerSocketId;
        const playerOrderIndex = game.playerOrder.indexOf(oldPlayerSocketId);
        
        // Log per debug del problema delle carte duplicate
        if (playerData && playerData.hand) {
            console.log(`[RECONNECTION BUG FIX] Player ${oldPlayerSocketId} aveva ${playerData.hand.length} carte prima della riconnessione`);
            console.log(`[RECONNECTION BUG FIX] NON ripristino la mano per evitare carte duplicate`);
        }
        
        // Remove the old player
        delete game.players[oldPlayerSocketId];
        delete game.playerColors[oldPlayerSocketId];
        delete game.playerNames[oldPlayerSocketId];
        delete game.playerRatings[oldPlayerSocketId];
        
        // Add player back with new socket ID, maintaining the original color
        // ✅ CORREZIONE: Non ripristiniamo la mano - sarà aggiornata dal Game.getState()
        game.players[newSocketId] = {
            id: newSocketId,
            socketId: newSocketId,
            userId: userId,
            color: color,
            hand: [], // Mano vuota temporanea - sarà popolata correttamente da Game.addPlayer()
            score: playerScore
        };
        
        // Restore player mappings
        game.playerColors[newSocketId] = color;
        game.playerNames[newSocketId] = playerName;
        if (disconnectedPlayers[userId].rating !== undefined) {
            game.playerRatings[newSocketId] = disconnectedPlayers[userId].rating;
        }
        
        // Update player order array
        if (playerOrderIndex !== -1) {
            game.playerOrder[playerOrderIndex] = newSocketId;
        }
        
        // Update player game mapping
        playerGameMap[newSocketId] = gameId;
        
        // Clean up disconnected player data
        delete disconnectedPlayers[userId];
        delete userIdToSocketId[userId];
        
        // Return success with game state
        const gameState = game.getState(newSocketId, false);
        
        // Add turn timer information
        if (gameTurnTimeouts[gameId] && gameTurnTimeouts[gameId].endTime) {
            gameState.turnTimeRemaining = Math.max(0, Math.floor((gameTurnTimeouts[gameId].endTime - Date.now()) / 1000));
            gameState.turnTimeout = gameTurnTimeouts[gameId].timeout;
        }
        
        return {
            event: 'reconnectionSuccessful',
            gameId,
            gameState,
            reconnectionTime: disconnectionDuration.toFixed(2),
            newPlayerId: newSocketId // Include the new player ID for the client
        };
    }
    
    /**
     * Check for expired disconnections and remove players if needed
     */
    checkDisconnectedPlayers() {
        const now = Date.now();
        
        for (const userId in disconnectedPlayers) {
            const { gameId, socketId, disconnectedAt, color } = disconnectedPlayers[userId];
            const disconnectionDuration = (now - disconnectedAt) / 1000; // in seconds
            
            // Check if we're approaching the grace period limit
            if (disconnectionDuration > RECONNECTION_GRACE_PERIOD) {
                console.log(`Grace period expired for userId ${userId} in game ${gameId} (${disconnectionDuration.toFixed(2)}s)`);
                
                // Get the game
                const game = activeGames[gameId];
                if (game && !game.gameOver) {
                    // Determine the opponent (the player who will win)
                    const opponentId = Object.keys(game.players).find(id => game.playerColors[id] !== color);
                    
                    if (opponentId) {
                        // End the game with the opponent as winner due to disconnection timeout
                        console.log(`Ending game ${gameId} due to disconnection timeout. Winner: ${opponentId}`);
                        game.endGame(opponentId, `Player ${color} disconnection timeout`);
                        
                        // Save final game state
                        this.saveGameState(gameId);
                        
                        // Clear turn timeout
                        this.clearTurnTimeout(gameId);
                    }
                }
                
                // Complete the removal of the player
                this.finalizePlayerRemoval(socketId, gameId);
                
                // Clean up disconnected player data
                delete disconnectedPlayers[userId];
                delete userIdToSocketId[userId];
            }
            // If we're approaching the grace period limit (75% of the way through),
            // send a warning to the opponent if the game is still active
            else if (disconnectionDuration > (RECONNECTION_GRACE_PERIOD * 0.75) && !disconnectedPlayers[userId].warningIssued) {
                const game = activeGames[gameId];
                if (game && !game.gameOver) {
                    // Mark that we've issued a warning for this disconnection
                    disconnectedPlayers[userId].warningIssued = true;
                    
                    // Calculate time remaining
                    const remainingTime = Math.ceil(RECONNECTION_GRACE_PERIOD - disconnectionDuration);
                    
                    console.log(`Issuing warning for disconnected player ${userId} in game ${gameId}. Time remaining: ${remainingTime}s`);
                    
                    // Return an event for the server to emit to the opponent
                    return {
                        event: 'disconnectionWarning',
                        gameId,
                        userId,
                        remainingTime,
                        playerColor: color
                    };
                }
            }
        }
        
        return null;
    }
    
    /**
     * Finalize player removal after grace period expires
     * @param {string} socketId - Socket ID
     * @param {string} gameId - Game ID
     */
    finalizePlayerRemoval(socketId, gameId) {
        const game = activeGames[gameId];
        if (!game || game.gameOver) return;
        
        // Get player color
        const playerColor = game.playerColors[socketId];
        
        // Remove player from game
        delete game.players[socketId];
        delete game.playerColors[socketId];
        
        // Remove from player order
        game.playerOrder = game.playerOrder.filter(id => id !== socketId);
        
        // End game if started
        if (game.gameStarted && !game.gameOver) {
            // Determine winner (the remaining player)
            const remainingPlayerId = Object.keys(game.players)[0];
            
            if (remainingPlayerId) {
                // End game with remaining player as winner due to disconnection
                game.endGame(remainingPlayerId, `Player ${playerColor} disconnected`);
                
                // Save final game state
                this.saveGameState(gameId);
                
                // Clear turn timeout
                this.clearTurnTimeout(gameId);
                
                console.log(`Game ${gameId} ended due to player ${socketId} disconnection timeout`);
            }
        }
        
        // If no players left, remove game
        if (Object.keys(game.players).length === 0) {
            delete activeGames[gameId];
            delete gameTurnTimeouts[gameId];
            console.log(`Game ${gameId} removed as all players left`);
        }
        
        // Remove player-game mapping
        delete playerGameMap[socketId];
    }
    /**
     * Create a new game
     * @param {Object} options - Game options
     * @returns {Object} - Game info
     */
    createGame(options = {}) {
        const { isPrivate = false, maxPlayers = 2, turnTimeout = DEFAULT_TURN_TIMEOUT } = options;

        // Generate a unique game ID (short 6-character ID)
        const gameId = this.generateShortGameId();

        // Generate an invite code for private games
        const inviteCode = isPrivate ? this.generateInviteCode() : null;

        // Create a new game instance
        const game = new Game(gameId, 'online');
        activeGames[gameId] = game;

        console.log(`Game created: ${gameId}, Private: ${isPrivate}, Invite Code: ${inviteCode}`);

        return {
            gameId,
            inviteCode,
            maxPlayers,
            turnTimeout,
            isPrivate
        };
    }

    /**
     * Add a player to a game
     * @param {string} gameId - Game ID
     * @param {string} socketId - Socket ID
     * @param {string} playerName - Player name
     * @param {number} rating - Player rating
     * @returns {boolean} - Success status
     */
    addPlayerToGame(gameId, socketId, playerName, rating = 1000, userId = null) {
        const game = activeGames[gameId];
        if (!game) {
            console.error(`Game ${gameId} not found`);
            return false;
        }

        // Check if game is full
        if (Object.keys(game.players).length >= 2) {
            console.error(`Game ${gameId} is full`);
            return false;
        }

        // Determine player color
        const isFirstPlayer = Object.keys(game.players).length === 0;
        const color = isFirstPlayer ? 'white' : 'black';

        // Add player to game with userId
        game.addPlayer(socketId, playerName, userId);

        // Store player rating
        game.playerRatings[socketId] = rating;

        // Map socket ID to game ID
        playerGameMap[socketId] = gameId;

        console.log(`Player ${socketId} (${playerName}) added to game ${gameId} as ${color}`);
        console.log(`Updated playerGameMap: ${socketId} -> ${gameId}`);

        // Start game if full
        if (Object.keys(game.players).length === 2) {
            this.startGame(gameId);
        }

        return true;
    }

    /**
     * Start a game
     * @param {string} gameId - Game ID
     * @returns {Object} - Start result
     */
    startGame(gameId) {
        const game = activeGames[gameId];
        if (!game) {
            console.error(`Game ${gameId} not found`);
            return { success: false, reason: "Game not found" };
        }

        // Start the game
        const result = game.startGame();

        if (result.success) {
            console.log(`Game ${gameId} started`);

            // Start turn timeout
            this.startTurnTimeout(gameId);
        } else {
            console.error(`Failed to start game ${gameId}: ${result.reason}`);
        }

        return result;
    }

    /**
     * Get a game instance
     * @param {string} gameId - Game ID
     * @returns {Game|null} - Game instance or null if not found
     */
    getGame(gameId) {
        const game = activeGames[gameId];
        if (!game) {
            console.error(`Game ${gameId} not found`);
            return null;
        }
        return game;
    }

    /**
     * Get game state for a player
     * @param {string} gameId - Game ID
     * @param {string} socketId - Socket ID (null for full state)
     * @returns {Object|null} - Game state or null if not found
     */
    getGameState(gameId, socketId = null) {
        const game = activeGames[gameId];
        if (!game) {
            console.error(`Game ${gameId} not found`);
            return null;
        }

        // Get game state
        const state = game.getState(socketId, false);
        
        // Debug: log del board state
        console.log(`[CONTROLLER DEBUG] Board state in getGameState:`, JSON.stringify(state.board));

        // Add turn timer information
        if (gameTurnTimeouts[gameId] && gameTurnTimeouts[gameId].endTime) {
            state.turnTimeRemaining = Math.max(0, Math.floor((gameTurnTimeouts[gameId].endTime - Date.now()) / 1000));
            state.turnTimeout = gameTurnTimeouts[gameId].timeout;
        }

        // Log dettagliato per debug delle mani
        console.log(`[CONTROLLER DEBUG] getGameState per ${socketId || 'tutti'} in partita ${gameId}`);
        if (state && state.players) {
            Object.keys(state.players).forEach(playerId => {
                const player = state.players[playerId];
                console.log(`[CONTROLLER DEBUG] Giocatore ${playerId} (${player.color}):`);

                if (player.hand) {
                    console.log(`[CONTROLLER DEBUG]   - Mano: ${player.hand.length} carte`);
                    console.log(`[CONTROLLER DEBUG]   - Dettaglio carte:`, JSON.stringify(player.hand.map(c => `${c.value}${c.suit}`)));
                } else {
                    console.log(`[CONTROLLER DEBUG]   - Mano: NON PRESENTE`);
                }
            });
        }

        return state;
    }

    /**
     * Handle a player action
     * @param {string} gameId - Game ID
     * @param {string} socketId - Socket ID
     * @param {string} action - Action type
     * @param {Object} actionData - Action data
     * @returns {Object} - Action result
     */
    handlePlayerAction(gameId, socketId, action, actionData) {
        console.log(`[GAME CONTROLLER] Handling player action: ${action} by player ${socketId} in game ${gameId}`);

        const game = activeGames[gameId];
        if (!game) {
            console.error(`[GAME CONTROLLER] Game ${gameId} not found for action ${action}`);
            return { success: false, reason: "Game not found" };
        }

        // Log player game mapping for debugging
        console.log(`[GAME CONTROLLER] Current playerGameMap for ${socketId}: ${playerGameMap[socketId]}`);
        console.log(`[GAME CONTROLLER] Game mode: ${game.mode}, Game started: ${game.gameStarted}, Game over: ${game.gameOver}`);

        // Validate action
        const validationResult = this.validateAction(game, socketId, action, actionData);
        if (!validationResult.valid) {
            console.error(`[GAME CONTROLLER] Invalid action ${action} by ${socketId} in game ${gameId}: ${validationResult.reason}`);
            return { success: false, reason: validationResult.reason };
        }

        console.log(`[GAME CONTROLLER] Action ${action} validation successful for player ${socketId}`);

        // Log current player information
        const currentPlayer = game.getCurrentPlayer();
        if (currentPlayer) {
            console.log(`[GAME CONTROLLER] Current player: ${currentPlayer.id} (${currentPlayer.color}), Hand size: ${currentPlayer.hand.length}`);
        }

        // Handle action based on type
        let result;

        switch (action) {
            case 'placeCard':
                console.log(`[PLACE CARD DEBUG] Processing placeCard for player ${socketId} in game ${gameId}`);
                console.log(`[PLACE CARD DEBUG] Card: ${actionData.card.suit}-${actionData.card.value}, Position: ${actionData.position}`);
                result = game.handlePlaceCard(socketId, actionData.card, actionData.position);
                console.log(`[PLACE CARD DEBUG] Result: success=${result.success}, reason=${result.reason || 'none'}`);
                
                // Enhance the result with vertex control information for PSN notation
                if (result.success) {
                    // Check if a vertex was just controlled with this move
                    const position = actionData.position;
                    
                    // Get player color for determining isWhite
                    const playerColor = game.players[socketId]?.color;
                    
                    // For normal moves, check if any vertex was controlled
                    if (result.gainedControl) {
                        console.log(`[GAME CONTROLLER] Move gained control of vertex: ${result.gainedControl}`);
                        // Add gained vertex control flag to the result explicitly for PSN
                        result.gainedVertexControl = true;
                        result.vertexPosition = result.gainedControl.replace('vertex-', '');
                        result.isWhite = playerColor === 'white';
                    }
                    // For vertex placements or vertexes gained through other means
                    else if (VERTEX_POSITIONS.includes(position) && game.board.vertexControl[`vertex-${position}`] === playerColor) {
                        console.log(`[GAME CONTROLLER] Move on vertex position: ${position}`);
                        // Add gained vertex control flag to the result explicitly for PSN
                        result.gainedVertexControl = true;
                        result.vertexPosition = position;
                        result.isWhite = playerColor === 'white';
                    }
                }
                break;
                
            case 'drawCard':
                result = game.handleDrawCard(socketId);
                // handleDrawCard already handles the nextTurn() internally
                if (!result.success) {
                    console.log(`[GAME CONTROLLER] drawCard action failed: ${result.reason}`);
                }
                break;
            case 'placeCardOnVertex':
                result = game.handlePlaceCardOnVertex(socketId, actionData.card, actionData.vertexId);
                break;
            case 'ribaltoneMove':
                result = game.handleRibaltoneMove(socketId, actionData.card, actionData.position);
                break;
            case 'passTurn':
                game.nextTurn();
                result = { success: true };
                break;
            case 'resign':
                // Get opponent ID (the winner)
                const opponentId = Object.keys(game.players).find(id => id !== socketId);
                if (!opponentId) {
                    result = { success: false, reason: "No opponent found" };
                } else {
                    // End game with opponent as winner
                    const reason = actionData && actionData.reason === 'timeout' ? 
                        'Timeout' : 'Abbandono';
                    game.endGame(opponentId, reason);
                    result = { success: true };
                }
                break;
            default:
                result = { success: false, reason: "Unknown action" };
        }

        // Reset turn timeout if action was successful
        if (result.success) {
            this.resetTurnTimeout(gameId);

            // Save game state after successful action
            this.saveGameState(gameId);
            
            // Check if game is over after the action
            if (game.gameOver) {
                console.log(`[GAME CONTROLLER] Game ${gameId} ended. Winner: ${game.winner}, Reason: ${game.winReason}`);
                
                // Clear turn timeout since game is over
                this.clearTurnTimeout(gameId);
                
                // Update ratings for multiplayer games
                const ratingUpdates = game.updateRatings(game.winner);
                
                if (ratingUpdates) {
                    console.log(`[GAME CONTROLLER] Sending gameOver event to all players in game ${gameId}`);
                    
                    // Emit gameOver event to all players in the game
                    if (this.io) {
                        this.io.to(`game:${gameId}`).emit('gameOver', {
                            gameId,
                            winnerId: game.winner,
                            winReason: game.winReason,
                            winCondition: game.winCondition,
                            ratingUpdates: ratingUpdates
                        });
                    } else {
                        console.error(`[GAME CONTROLLER] Socket.IO instance not available to emit gameOver event`);
                    }
                } else {
                    console.log(`[GAME CONTROLLER] No rating updates available for game ${gameId}`);
                    
                    // Still emit gameOver event even without rating updates
                    if (this.io) {
                        this.io.to(`game:${gameId}`).emit('gameOver', {
                            gameId,
                            winnerId: game.winner,
                            winReason: game.winReason,
                            winCondition: game.winCondition
                        });
                    }
                }
            }
        }

        return result;
    }

    /**
     * Validate a player action
     * @param {Game} game - Game instance
     * @param {string} socketId - Socket ID
     * @param {string} action - Action type
     * @param {Object} actionData - Action data
     * @returns {Object} - Validation result
     */
    validateAction(game, socketId, action, actionData) {
        // Check if game is active
        if (!game.gameStarted || game.gameOver) {
            return { valid: false, reason: "Game is not active" };
        }

        // Check if it's the player's turn
        const currentPlayer = game.getCurrentPlayer();
        console.log(`[VALIDATE ACTION DEBUG] Current player: ${currentPlayer?.id}, Requesting player: ${socketId}, Match: ${currentPlayer?.id === socketId}`);
        console.log(`[VALIDATE ACTION DEBUG] All players in game:`, Object.keys(game.players));
        console.log(`[VALIDATE ACTION DEBUG] Player order:`, game.playerOrder);
        if (!currentPlayer || currentPlayer.id !== socketId) {
            console.log(`[VALIDATE ACTION DEBUG] Turn validation failed - current: ${currentPlayer?.id}, requested: ${socketId}`);
            return { valid: false, reason: "Not your turn" };
        }

        // Validate specific actions
        switch (action) {
            case 'placeCard':
                return this.validatePlaceCardAction(game, socketId, actionData);
            case 'drawCard':
                return this.validateDrawCardAction(game, socketId);
            case 'placeCardOnVertex':
                return this.validatePlaceCardOnVertexAction(game, socketId, actionData);
            case 'ribaltoneMove':
                return this.validateRibaltoneMoveAction(game, socketId, actionData);
            case 'passTurn':
                return { valid: true };
            case 'resign':
                return { valid: true }; // Always valid, player can resign at any time
            default:
                return { valid: false, reason: "Unknown action" };
        }
    }

    /**
     * Validate a placeCard action
     * @param {Game} game - Game instance
     * @param {string} socketId - Socket ID
     * @param {Object} actionData - Action data
     * @returns {Object} - Validation result
     */
    validatePlaceCardAction(game, socketId, actionData) {
        const { card, position } = actionData;

        // Check if card exists in player's hand
        const player = game.players[socketId];
        if (!player) {
            return { valid: false, reason: "Player not found" };
        }

        const cardIndex = player.hand.findIndex(c =>
            c.suit === card.suit && c.value === card.value
        );

        if (cardIndex === -1) {
            return { valid: false, reason: "Card not in hand" };
        }

        // Check if position is valid
        if (!game.board.isValidPosition(position)) {
            return { valid: false, reason: "Invalid position" };
        }

        // Check if position is occupied
        if (game.board.isOccupied(position)) {
            return { valid: false, reason: "Position already occupied" };
        }

        return { valid: true };
    }

    /**
     * Validate a drawCard action
     * @param {Game} game - Game instance
     * @param {string} socketId - Socket ID
     * @returns {Object} - Validation result
     */
    validateDrawCardAction(game, socketId) {
        console.log(`[GAME CONTROLLER] Validating drawCard action for player ${socketId} in game ${game.gameId}`);

        // Check if deck has cards
        if (game.deck.isEmpty()) {
            console.log(`[GAME CONTROLLER] drawCard validation failed: Deck is empty`);
            return { valid: false, reason: "Deck is empty" };
        }

        // Check if player's hand is full
        const player = game.players[socketId];
        if (!player) {
            console.log(`[GAME CONTROLLER] drawCard validation failed: Player ${socketId} not found`);
            return { valid: false, reason: "Player not found" };
        }

        if (player.hand.length >= 10) {
            console.log(`[GAME CONTROLLER] drawCard validation failed: Player ${socketId} hand is full (${player.hand.length} cards)`);
            return { valid: false, reason: "Hand is full" };
        }

        console.log(`[GAME CONTROLLER] drawCard validation successful for player ${socketId}`);
        return { valid: true };
    }

    /**
     * Validate a placeCardOnVertex action
     * @param {Game} game - Game instance
     * @param {string} socketId - Socket ID
     * @param {Object} actionData - Action data
     * @returns {Object} - Validation result
     */
    validatePlaceCardOnVertexAction(game, socketId, actionData) {
        const { card, vertexId } = actionData;

        // Check if card exists in player's hand
        const player = game.players[socketId];
        if (!player) {
            return { valid: false, reason: "Player not found" };
        }

        const cardIndex = player.hand.findIndex(c =>
            c.suit === card.suit && c.value === card.value
        );

        if (cardIndex === -1) {
            return { valid: false, reason: "Card not in hand" };
        }

        // Check if vertex is valid
        const validVertices = ["vertex-a1", "vertex-f1", "vertex-a6", "vertex-f6"];
        if (!validVertices.includes(vertexId)) {
            return { valid: false, reason: "Invalid vertex" };
        }

        // Check if vertex is controlled by player or is free
        const vertexControl = game.board.vertexControl[vertexId];
        const playerColor = game.playerColors[socketId];

        if (vertexControl && vertexControl !== playerColor) {
            return { valid: false, reason: "Vertex controlled by opponent" };
        }

        return { valid: true };
    }

    /**
     * Validate a ribaltoneMove action
     * @param {Game} game - Game instance
     * @param {string} socketId - Socket ID
     * @param {Object} actionData - Action data
     * @returns {Object} - Validation result
     */
    validateRibaltoneMoveAction(game, socketId, actionData) {
        const { card, position } = actionData;

        // Check if we're in ribaltone mode
        if (!game.continueForOneTurn) {
            return { valid: false, reason: "Not in ribaltone mode" };
        }

        // Check if card exists in player's hand
        const player = game.players[socketId];
        if (!player) {
            return { valid: false, reason: "Player not found" };
        }

        const cardIndex = player.hand.findIndex(c =>
            c.suit === card.suit && c.value === card.value
        );

        if (cardIndex === -1) {
            return { valid: false, reason: "Card not in hand" };
        }

        // Check if position is valid
        if (!game.board.isValidPosition(position)) {
            return { valid: false, reason: "Invalid position" };
        }

        // Check if position is a corner
        if (!game.board.isCornerPosition(position)) {
            return { valid: false, reason: "Position is not a corner" };
        }

        // Check if position is occupied by opponent's card
        const cardOnTarget = game.board.getCardAt(position);
        const opponentColor = player.color === 'white' ? 'black' : 'white';

        if (!cardOnTarget || cardOnTarget.ownerColor !== opponentColor) {
            return { valid: false, reason: "Position not occupied by opponent's card" };
        }

        return { valid: true };
    }

    /**
     * Remove a player from a game
     * @param {string} socketId - Socket ID
     * @param {string} userId - User ID (if available)
     * @returns {Object|null} - Game state or null if not found
     */
    removePlayer(socketId, userId = null) {
        const gameId = playerGameMap[socketId];
        if (!gameId) {
            console.log(`No game found for player ${socketId}`);
            return null;
        }

        const game = activeGames[gameId];
        if (!game) {
            console.error(`Game ${gameId} not found`);
            delete playerGameMap[socketId];
            return null;
        }

        // Check if game is already over
        if (game.gameOver) {
            delete playerGameMap[socketId];
            return null;
        }

        // Get player color
        const playerColor = game.playerColors[socketId];
        
        // If user ID is provided, mark as temporarily disconnected instead of removing
        if (userId) {
            console.log(`Player ${socketId} (userId: ${userId}) temporarily disconnected from game ${gameId}`);
            
            // Store player's information for potential reconnection
            disconnectedPlayers[userId] = {
                gameId,
                socketId,
                disconnectedAt: Date.now(),
                color: playerColor,
                playerName: game.playerNames[socketId] || 'Player',
                rating: game.playerRatings[socketId] || 1000
            };
            
            // Store the mapping for reconnection
            userIdToSocketId[userId] = socketId;
            
            // Don't remove player immediately, just mark as disconnected and start grace period
            // Let the turn timer continue to run
            
            // Notify other players about the disconnection
            return {
                event: 'playerDisconnected',
                gameId,
                playerId: socketId,
                playerColor,
                reconnectionTimeLeft: RECONNECTION_GRACE_PERIOD
            };
        }
        
        // For players without userId or after grace period, remove completely
        console.log(`Player ${socketId} permanently removed from game ${gameId}`);
        
        // Remove player from game
        delete game.players[socketId];
        delete game.playerColors[socketId];

        // Remove from player order
        game.playerOrder = game.playerOrder.filter(id => id !== socketId);

        // End game if started
        if (game.gameStarted && !game.gameOver) {
            // Determine winner (the remaining player)
            const remainingPlayerId = Object.keys(game.players)[0];

            if (remainingPlayerId) {
                // End game with remaining player as winner
                game.endGame(remainingPlayerId, `Player ${playerColor} disconnected`);

                // Save final game state
                this.saveGameState(gameId);

                // Clear turn timeout
                this.clearTurnTimeout(gameId);

                console.log(`Game ${gameId} ended due to player ${socketId} disconnection`);

                // Return final game state
                return game.getState(null, false);
            }
        }

        // If no players left, remove game
        if (Object.keys(game.players).length === 0) {
            delete activeGames[gameId];
            delete gameTurnTimeouts[gameId];
            console.log(`Game ${gameId} removed as all players left`);
        }

        // Remove player-game mapping
        delete playerGameMap[socketId];

        return null;
    }

    /**
     * Find a match for a player
     * @param {Object} playerInfo - Player info
     * @returns {Object|null} - Match info or null if no match found
     */
    findMatch(playerInfo) {
        const { id: socketId, userId, name, rating } = playerInfo;

        // Check for waiting games that need one more player
        for (const gameId in activeGames) {
            const game = activeGames[gameId];

            // Skip games that are private, already started, or full
            if (game.mode !== 'online' || game.gameStarted || Object.keys(game.players).length >= 2) {
                continue;
            }

            // Get the waiting player
            const waitingPlayerId = Object.keys(game.players)[0];
            if (!waitingPlayerId) continue;

            const waitingPlayerRating = game.playerRatings[waitingPlayerId] || 1000;

            // Check rating difference (within 200 points)
            if (Math.abs(waitingPlayerRating - rating) > 200) {
                continue;
            }

            // Add player to game
            const success = this.addPlayerToGame(gameId, socketId, name, rating);

            if (success) {
                return {
                    gameId,
                    opponent: {
                        id: waitingPlayerId,
                        name: game.playerNames[waitingPlayerId] || 'Opponent',
                        rating: waitingPlayerRating
                    }
                };
            }
        }

        // No match found, create a new game
        const gameInfo = this.createGame({ isPrivate: false });
        this.addPlayerToGame(gameInfo.gameId, socketId, name, rating);

        // No match yet
        return null;
    }

    /**
     * Start turn timeout
     * @param {string} gameId - Game ID
     * @param {number} timeout - Timeout in seconds
     */
    startTurnTimeout(gameId, timeout = null) {
        const game = activeGames[gameId];
        if (!game || game.gameOver) return;

        // Clear existing timeout
        this.clearTurnTimeout(gameId);

        // Get current player
        const currentPlayer = game.getCurrentPlayer();
        if (!currentPlayer) return;

        // Use provided timeout or get from game session
        const turnTimeout = timeout || DEFAULT_TURN_TIMEOUT;

        // Set timeout
        const timeoutId = setTimeout(() => {
            this.handleTurnTimeout(gameId, currentPlayer.id);
        }, turnTimeout * 1000);

        // Store timeout info
        gameTurnTimeouts[gameId] = {
            timeoutId,
            playerId: currentPlayer.id,
            startTime: Date.now(),
            endTime: Date.now() + (turnTimeout * 1000),
            timeout: turnTimeout
        };

        console.log(`Turn timeout started for player ${currentPlayer.id} in game ${gameId}: ${turnTimeout}s`);
    }

    /**
     * Reset turn timeout
     * @param {string} gameId - Game ID
     */
    resetTurnTimeout(gameId) {
        const game = activeGames[gameId];
        if (!game || game.gameOver) return;

        // Clear existing timeout
        this.clearTurnTimeout(gameId);

        // Start new timeout
        this.startTurnTimeout(gameId);
    }

    /**
     * Clear turn timeout
     * @param {string} gameId - Game ID
     */
    clearTurnTimeout(gameId) {
        if (gameTurnTimeouts[gameId]) {
            clearTimeout(gameTurnTimeouts[gameId].timeoutId);
            delete gameTurnTimeouts[gameId];
        }
    }

    /**
     * Handle turn timeout
     * @param {string} gameId - Game ID
     * @param {string} playerId - Player ID
     */
    handleTurnTimeout(gameId, playerId) {
        const game = activeGames[gameId];
        if (!game || game.gameOver) return;

        // Check if it's still the same player's turn
        const currentPlayer = game.getCurrentPlayer();
        if (!currentPlayer || currentPlayer.id !== playerId) return;

        console.log(`Turn timeout for player ${playerId} in game ${gameId}`);

        // Get opponent ID (the winner)
        const opponentId = Object.keys(game.players).find(id => id !== playerId);
        
        if (opponentId) {
            // End game with opponent as winner due to timeout
            game.endGame(opponentId, 'Timeout');
            console.log(`Game ${gameId} ended due to player ${playerId} timeout`);
        }

        // Save game state
        this.saveGameState(gameId);

        // Return updated game state
        return game.getState(null, false);
    }

    /**
     * Save game state to database
     * @param {string} gameId - Game ID
     */
    async saveGameState(gameId) {
        const game = activeGames[gameId];
        if (!game) return;

        try {
            // Get full game state
            const state = game.getState(null, false);

            // Save to database
            await GameSession.saveGameState(gameId, state);

            console.log(`Game state saved for game ${gameId}`);
        } catch (error) {
            console.error(`Error saving game state for game ${gameId}:`, error);
        }
    }

    /**
     * Generate a unique invite code
     * @returns {string} - Invite code
     */
    generateInviteCode() {
        // Generate a 6-character alphanumeric code
        const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
        let code = '';
        for (let i = 0; i < 6; i++) {
            code += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return code;
    }

    /**
     * Get game by invite code
     * @param {string} inviteCode - Invite code
     * @returns {Object|null} - Game info or null if not found
     */
    getGameByInviteCode(inviteCode) {
        for (const gameId in activeGames) {
            const game = activeGames[gameId];
            if (game.inviteCode === inviteCode) {
                return {
                    gameId,
                    playerCount: Object.keys(game.players).length,
                    isStarted: game.gameStarted,
                    isOver: game.gameOver
                };
            }
        }
        return null;
    }

    /**
     * Get all active games
     * @returns {Array} - Array of game info
     */
    getAllActiveGames() {
        const games = [];

        for (const gameId in activeGames) {
            const game = activeGames[gameId];

            // Skip completed games
            if (game.gameOver) continue;

            games.push({
                gameId,
                mode: game.mode,
                playerCount: Object.keys(game.players).length,
                isStarted: game.gameStarted,
                createdAt: game.createdAt || new Date()
            });
        }

        return games;
    }

    /**
     * Clean up completed games
     * @param {number} maxAgeMinutes - Maximum age in minutes
     * @returns {number} - Number of games removed
     */
    cleanupCompletedGames(maxAgeMinutes = 30) {
        let count = 0;
        const now = Date.now();

        for (const gameId in activeGames) {
            const game = activeGames[gameId];

            // Skip active games
            if (!game.gameOver) continue;

            // Check if game is old enough to remove
            const gameEndTime = game.endedAt || now;
            const ageMinutes = (now - gameEndTime) / (1000 * 60);

            if (ageMinutes >= maxAgeMinutes) {
                // Remove game
                delete activeGames[gameId];
                delete gameTurnTimeouts[gameId];
                count++;
            }
        }

        return count;
    }

    /**
     * Generate a short game ID
     * @returns {string} - 6-character game ID
     */
    generateShortGameId() {
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        
        // Genera un ID di 6 caratteri
        for (let i = 0; i < 6; i++) {
            result += characters.charAt(Math.floor(Math.random() * characters.length));
        }
        
        // Verifica che l'ID sia unico
        if (activeGames[result]) {
            // Se esiste già, genera ricorsivamente un nuovo ID
            return this.generateShortGameId();
        }
        
        return result;
    }
}

module.exports = new GameController();
