 [IMMEDIATE NAMES] Skip aggiornamento nomi - già presenti nomi permanenti validi
 [IMMEDIATE NAMES] Nomi permanenti attuali: {"BZ0EG04KJTrrcpBzAAAR":"bruscolino","ZqI2Xh9KZ6P4Xvw9AAAK":"giggio"}
 [PLAYER AREAS DEBUG] Aggiornamento nomi chiamato
 [PLAYER AREAS DEBUG] effectiveState.playerColors: Object
 [PLAYER AREAS DEBUG] effectiveState.playerNames: Object
 [PLAYER AREAS DEBUG] namesLocked: false
 [PLAYER AREAS] Aggiorno aree giocatori in modalità online
 [PLAYER AREAS DEBUG] effectiveState.playerColors: {"BZ0EG04KJTrrcpBzAAAR":"black","ZqI2Xh9KZ6P4Xvw9AAAK":"white"}
 [PLAYER AREAS DEBUG] ID: BZ0EG04KJTrrcpBzAAAR, Color: black
 [PLAYER AREAS DEBUG] ID: ZqI2Xh9KZ6P4Xvw9AAAK, Color: white
 [PLAYER AREAS DEBUG] whitePlayerId: ZqI2Xh9KZ6P4Xvw9AAAK, blackPlayerId: BZ0EG04KJTrrcpBzAAAR
 [PLAYER AREAS DEBUG] Nomi permanenti disponibili: {"BZ0EG04KJTrrcpBzAAAR":"bruscolino","ZqI2Xh9KZ6P4Xvw9AAAK":"giggio"}
 [PLAYER AREAS DEBUG] Cerco nome per whitePlayerId: ZqI2Xh9KZ6P4Xvw9AAAK
 [PLAYER AREAS DEBUG] Cerco nome per blackPlayerId: BZ0EG04KJTrrcpBzAAAR
 [PLAYER AREAS DEBUG] Trovato nome permanente per bianco: giggio
 [PLAYER AREAS DEBUG] Trovato nome permanente per nero: bruscolino
 [PLAYER AREAS] Imposto nomi: giggio (bianco), bruscolino (nero)
 [PLAYER AREAS] player1-area riceverà: giggio (BIANCO)
 [PLAYER AREAS] player2-area riceverà: bruscolino (NERO)
 [PLAYER AREAS DOM] PRIMA dell'aggiornamento:
 [PLAYER AREAS DOM] player1NameElement.textContent = "..."
 [PLAYER AREAS DOM] player2NameElement.textContent = "..."
 [NAMES PROTECTION] Tentativo di modificare player1 textContent: giggio
 [NAMES PROTECTION] Tentativo di modificare player2 textContent: bruscolino
 [PLAYER AREAS DOM] DOPO l'aggiornamento:
 [PLAYER AREAS DOM] player1NameElement.textContent = "giggio"
 [PLAYER AREAS DOM] player2NameElement.textContent = "bruscolino"
 [PLAYER AREAS DOM] Impostato: player1="giggio" (bianco), player2="bruscolino" (nero)
 [PLAYER AREAS] Nomi definitivi bloccati: bianco=giggio, nero=bruscolino
 [PLAYER AREAS] namesFinallySet = true - Nessuna ulteriore modifica ai nomi sarà permessa
 [PLAYER AREAS DEBUG] currentPlayerId: ZqI2Xh9KZ6P4Xvw9AAAK whitePlayerId: ZqI2Xh9KZ6P4Xvw9AAAK blackPlayerId: BZ0EG04KJTrrcpBzAAAR
 [PLAYER AREAS DEBUG] Aggiungendo current-player al giocatore bianco (player1)
 [PLAYER AREAS DEBUG] ✅ Aggiunta classe current-turn a player1-area per GLOW
 [PLAYER AREAS] Attivata animazione glow aggiungendo classe ready-for-play
 [PLAYER AREAS DEBUG] Post-applicazione classi:
 [PLAYER AREAS DEBUG] player1NameElement classes: player-name loaded current-player
 [PLAYER AREAS DEBUG] player2NameElement classes: player-name loaded
 [PLAYER AREAS DEBUG] Body ready-for-play: true
 [GAME STATE] isFirstStateReceived: true
 [GAME STATE] shouldShowDiceAnimation: c6
 [GAME STATE] Prima ricezione stato - Inizio animazione dadi
 [GAME STATE] showGameSetup già chiamato, skip
 [GAME STATE] Chiamando animateDiceRoll
 [DICE ANIMATION] animateDiceRoll chiamato - Animazione dei dadi commentata/disabilitata
 [DICE ANIMATION] Parametri: Object
 [DICE ANIM] Saltando animazione dadi, usando diceResult: Object e initialPosition: c6
 [DICE ANIM] Usando initialPosition (c6) -> Alpha: C, Numeric: 6
 [DICE ANIM] Dati dadi configurati:
 [DICE ANIM] Dado numerico mostrerà: 6
 [DICE ANIM] Dado alfabetico mostrerà: C
 [DICE ANIM] Dado colore mostrerà: black
 [DICE ANIM] initialPosition: c6
 [DICE ANIM] Saltata animazione dei dadi
 [DICE ANIM] Flag diceAnimationCompletedOnce impostato dopo salto animazione
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 [GAME STATE] Animazione dadi completata
 [CHAT] TEST: Inviando messaggio alla chat
 [MATCH FOUND] Flag processingGameData pulito
 [MULTIPLAYER] Dati di sessione rimossi dopo processamento
 [GAME STATE] Preparando transizione fluida da setup a game container
 [GAME STATE] Preparazione interfaccia multiplayer con pre-rendering
 [GAME STATE] Creazione tabellone nascosto per multiplayer
 [BOARD CREATION] Creazione nuovo tabellone di gioco - Stack trace: Error
    at createGameBoard (http://localhost:3000/script.js:5046:87)
    at http://localhost:3000/script.js:11054:21
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-a1
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-b1
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-c1
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-d1
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-e1
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-f1
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-a2
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-b2
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-c2
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-d2
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-e2
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-f2
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-a3
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-b3
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-c3
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-d3
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-e3
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-f3
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-a4
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-b4
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-c4
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-d4
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-e4
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-f4
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-a5
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-b5
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-c5
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-d5
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-e5
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-f5
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-a6
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-b6
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-c6
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-d6
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-e6
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-f6
 [GAME STATE] Game container pre-renderizzato e pronto per fade-in
 [GAME STATE] Setup-animation non trovato
 [GAME STATE] Mostrando game container dopo preparazione
 [DICE ANIMATION] showGameContainer() chiamato
 [DICE ANIMATION] Nascondo dice animation area con fade-out
 [DICE ANIMATION] Nascondo dice animation overlay
 [SHOWGAME] Nomi già impostati definitivamente, preservo i valori attuali
 [SHOWGAME] Game container finale: Object
 [SHOWGAME DEBUG] Celle esistenti: 36 Dovrebbe creare tabellone: false
 [SHOWGAME] Tabellone già esistente, skip creazione
 [SHOWGAME] Partita multiplayer - evito resetGameUI per prevenire doppio refresh
 [SHOWGAME] Container multiplayer in attesa, skip visualizzazione
 [GAME STATE] Avvio animazioni carte dopo i dadi
 [NAMES PROTECTION] animateCardDealing intercettato
 [NAMES PROTECTION] Salvato nome player 1: giggio
 [NAMES PROTECTION] Salvato nome player 2: bruscolino
 [NAMES PROTECTION] Protezione attivata
 [NAMES PROTECTION] Salvato nome player 1: giggio
 [NAMES PROTECTION] Salvato nome player 2: bruscolino
 [ANIM] Browser minimizzato durante animazione distribuzione carte
 [CARD DEALING] Deck area mostrata
 [NAMES PROTECTION] Tentativo di modificare player1 textContent: 
 [NAMES PROTECTION] BLOCCATO svuotamento player1, mantengo: giggio
 [NAMES PROTECTION] Tentativo di modificare player2 textContent: 
 [NAMES PROTECTION] BLOCCATO svuotamento player2, mantengo: bruscolino
 [NAMES PROTECTION] renderHand intercettato, cards: 0
 [NAMES PROTECTION] Rilevato tentativo di render mano vuota, attivo protezione immediata
 [NAMES PROTECTION] Salvato nome player 1: giggio
 [NAMES PROTECTION] Salvato nome player 2: bruscolino
 [NAMES PROTECTION] Protezione attivata
 [NAMES PROTECTION] Salvato nome player 1: giggio
 [NAMES PROTECTION] Salvato nome player 2: bruscolino
 [RENDER HAND] Rendering 0 cards to player1-hand, isClickable: false
 [RENDER HAND] Cards: Array(0)
 [BRUSCOLINO RENDER DEBUG] Rendering mano player1-hand, isClickable: false
 [NAMES PROTECTION] renderHand intercettato, cards: 0
 [NAMES PROTECTION] Rilevato tentativo di render mano vuota, attivo protezione immediata
 [NAMES PROTECTION] Salvato nome player 1: giggio
 [NAMES PROTECTION] Salvato nome player 2: bruscolino
 [NAMES PROTECTION] Protezione attivata
 [NAMES PROTECTION] Salvato nome player 1: giggio
 [NAMES PROTECTION] Salvato nome player 2: bruscolino
 [RENDER HAND] Rendering 0 cards to player2-hand, isClickable: false
 [RENDER HAND] Cards: Array(0)
 [BRUSCOLINO RENDER DEBUG] Rendering mano player2-hand, isClickable: false
 [NAMES PROTECTION] Tentativo di modificare player1 textContent: giggio
 [NAMES PROTECTION] Tentativo di modificare player2 textContent: bruscolino
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 [DECK IMAGE] Mazzo caricato con successo: http://localhost:3000/img/carte/card-back.webp
 [ANIMATION V2] Player 1, Card 0: SUCCESSO caricamento immagine: http://localhost:3000/img/carte/card-back.webp
 [ANIMATION V2] Image details: Object
 [FETCH TEST] Player 1, Card 0: Fetch test risultato: Object
 [FETCH TEST] Player 2, Card 0: Fetch test risultato: Object
 [FETCH TEST] Player 1, Card 1: Fetch test risultato: Object
 [FETCH TEST] Player 2, Card 1: Fetch test risultato: Object
 [FETCH TEST] Player 1, Card 2: Fetch test risultato: Object
 [FETCH TEST] Player 2, Card 2: Fetch test risultato: Object
 [FETCH TEST] Player 1, Card 3: Fetch test risultato: Object
 [FETCH TEST] Player 2, Card 3: Fetch test risultato: Object
 [FETCH TEST] Player 1, Card 4: Fetch test risultato: Object
 [FETCH TEST] Player 2, Card 4: Fetch test risultato: Object
 [ANIMATION V2] Player 1, Card 3: SUCCESSO caricamento immagine: http://localhost:3000/img/carte/card-back.webp
 [ANIMATION V2] Image details: Object
 [ANIMATION V2] Player 2, Card 1: SUCCESSO caricamento immagine: http://localhost:3000/img/carte/card-back.webp
 [ANIMATION V2] Image details: Object
 [ANIMATION V2] Player 2, Card 2: SUCCESSO caricamento immagine: http://localhost:3000/img/carte/card-back.webp
 [ANIMATION V2] Image details: Object
 [ANIMATION V2] Player 2, Card 4: SUCCESSO caricamento immagine: http://localhost:3000/img/carte/card-back.webp
 [ANIMATION V2] Image details: Object
 [ANIMATION V2] Player 1, Card 2: SUCCESSO caricamento immagine: http://localhost:3000/img/carte/card-back.webp
 [ANIMATION V2] Image details: Object
 [ANIMATION V2] Player 2, Card 3: SUCCESSO caricamento immagine: http://localhost:3000/img/carte/card-back.webp
 [ANIMATION V2] Image details: Object
 [ANIMATION V2] Player 2, Card 0: SUCCESSO caricamento immagine: http://localhost:3000/img/carte/card-back.webp
 [ANIMATION V2] Image details: Object
 [ANIMATION V2] Player 1, Card 1: SUCCESSO caricamento immagine: http://localhost:3000/img/carte/card-back.webp
 [ANIMATION V2] Image details: Object
 [ANIMATION V2] Player 1, Card 4: SUCCESSO caricamento immagine: http://localhost:3000/img/carte/card-back.webp
 [ANIMATION V2] Image details: Object
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 [MATCH FOUND] Skip secondo tentativo: gameRunning= undefined hasInitialState= true
 [GAME STATE] Reset flag processingFirstOnlineState
 [DICE STATUS DEBUG] Usando nomi permanenti: {"BZ0EG04KJTrrcpBzAAAR":"bruscolino","ZqI2Xh9KZ6P4Xvw9AAAK":"giggio"}
 [DICE STATUS DEBUG] Colori permanenti: {"BZ0EG04KJTrrcpBzAAAR":"black","ZqI2Xh9KZ6P4Xvw9AAAK":"white"}
 [DICE STATUS DEBUG] Dopo strategia 0 (permanente) - player1Name: giggio player2Name: bruscolino
 [DICE STATUS] Aggiornato stato dadi: C6 - Bianco: giggio, Nero: bruscolino
 [ANIMATION] Tutte le animazioni di distribuzione carte completate
 [ANIM] Avvio animazione carta iniziale: Object su c6
 [ANIM] Partita online rilevata - Controllo se devo saltare animazione
 [ANIM] window.myPlayerId: BZ0EG04KJTrrcpBzAAAR
 [ANIM] window.permanentPlayerColors: Object
 [ANIM] initialCard: Object
 [ANIM] initialPosition: c6
 [ANIM] Il mio colore: black
 [ANIM] CONTROLLO 1 - Carta non ha ownerColor valido: neutral
 [ANIM] CONTROLLO 2 - Nessuna carta esistente nel board state per posizione: c6
 [ANIM] CONTROLLO 3 - currentGameState o originalCurrentPlayerId non disponibili
 [ANIM] CONTROLLO 4 - lastMovePlayerId non disponibile nel currentGameState
 [ANIM] Continuo con animazione normale - tutti i controlli passati
 [ANIM] Browser minimizzato, continuo l'animazione senza effetti particellari
 [INITIAL CARD DEBUG] Base URL = http://localhost:3000
 [INITIAL CARD DEBUG] Percorsi da provare: Array(8)
 [INITIAL CARD] Primo tentativo: http://localhost:3000/img/carte/card-back.webp?cache=1750900528743
 [ANIM] Inizio animazione movimento carta iniziale.
 [INITIAL CARD] SUCCESSO caricamento immagine: http://localhost:3000/img/carte/card-back.webp?cache=1750900528743
 [INITIAL CARD] Image details: Object
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [GAME STATE] Ricevuto stato con proprietà: Array(26)
 [GAME STATE] currentPlayerId RAW from server: BZ0EG04KJTrrcpBzAAAR
 [GAME STATE] diceResult: Object
 [GAME STATE] initialPosition: c6
 [GAME STATE] mode: online
 [GAME STATE DEBUG] players: {"BZ0EG04KJTrrcpBzAAAR":{"id":"BZ0EG04KJTrrcpBzAAAR","color":"black","handSize":5,"score":0,"hand":[{"id":"Rock-5","suit":"Rock","value":"5"},{"id":"Paper-10","suit":"Paper","value":"10"},{"id":"Paper-K","suit":"Paper","value":"K"},{"id":"Paper-A","suit":"Paper","value":"A"},{"id":"Rock-4","suit":"Rock","value":"4"}]},"ZqI2Xh9KZ6P4Xvw9AAAK":{"id":"ZqI2Xh9KZ6P4Xvw9AAAK","color":"white","handSize":4,"score":0,"hand":[{"id":"Rock-2","suit":"Rock","value":"2"},{"id":"Paper-9","suit":"Paper","value":"9"},{"id":"Paper-Q","suit":"Paper","value":"Q"},{"id":"Paper-7","suit":"Paper","value":"7"}]}}
 [GAME STATE DEBUG] playerColors: {"BZ0EG04KJTrrcpBzAAAR":"black","ZqI2Xh9KZ6P4Xvw9AAAK":"white"}
 [GAME STATE DEBUG] playerNames: {"BZ0EG04KJTrrcpBzAAAR":"bruscolino","ZqI2Xh9KZ6P4Xvw9AAAK":"giggio"}
 [GAME STATE DEBUG] window.myPlayerId: BZ0EG04KJTrrcpBzAAAR
 [CARD PROTECTION] 🛡️ Protezione PRE-MAPPING attivata
 [CARD PROTECTION] 🔍 Inizio validazione stato di gioco MIGLIORATA...
 [CARD PROTECTION] 🔍 Modalità: online
 [CARD PROTECTION] 🔍 Giocatori presenti: Array(2)
 [CARD PROTECTION] 📋 Carte sul tabellone: 2
 [CARD PROTECTION] 📋 Paper-2 (2 di Paper) in b6
 [CARD PROTECTION] 📋 Rock-8 (8 di Rock) in c6
 [CARD PROTECTION] 👤 Controllo giocatore BZ0EG04KJTrrcpBzAAAR:
 [CARD PROTECTION] 👤 - Colore: black
 [CARD PROTECTION] 👤 - Carte in mano: 5
 [CARD PROTECTION] 👤 - Dettaglio carte: Array(5)
 [CARD PROTECTION] 👤 Controllo giocatore ZqI2Xh9KZ6P4Xvw9AAAK:
 [CARD PROTECTION] 👤 - Colore: white
 [CARD PROTECTION] 👤 - Carte in mano: 4
 [CARD PROTECTION] 👤 - Dettaglio carte: Array(4)
 [CARD PROTECTION] ✅ Stato consistente - nessuna duplicazione rilevata
 [ID SYNC] ID corretto confermato: BZ0EG04KJTrrcpBzAAAR per username: bruscolino
 [SYNC DEBUG] Received board state: {"a1":null,"b1":null,"c1":null,"d1":null,"e1":null,"f1":null,"a2":null,"b2":null,"c2":null,"d2":null,"e2":null,"f2":null,"a3":null,"b3":null,"c3":null,"d3":null,"e3":null,"f3":null,"a4":null,"b4":null,"c4":null,"d4":null,"e4":null,"f4":null,"a5":null,"b5":null,"c5":null,"d5":null,"e5":null,"f5":null,"a6":null,"b6":{"suit":"Paper","value":"2","id":"Paper-2","ownerColor":"white"},"c6":{"suit":"Rock","value":"8","id":"Rock-8","ownerColor":"neutral"},"d6":null,"e6":null,"f6":null}
 [SYNC DEBUG] Board keys: Array(36)
 [IMMEDIATE NAMES] Skip aggiornamento nomi - già presenti nomi permanenti validi
 [IMMEDIATE NAMES] Nomi permanenti attuali: {"BZ0EG04KJTrrcpBzAAAR":"bruscolino","ZqI2Xh9KZ6P4Xvw9AAAK":"giggio"}
 [PLAYER AREAS] Nomi definitivamente impostati, NON li tocco MAI
 [TURN INDICATOR] Aggiornamento solo indicatore turno
 [TURN INDICATOR] currentPlayerId ricevuto: BZ0EG04KJTrrcpBzAAAR
 [TURN INDICATOR] Fallback - whitePlayerId: ZqI2Xh9KZ6P4Xvw9AAAK blackPlayerId: BZ0EG04KJTrrcpBzAAAR
 [TURN INDICATOR] ✅ ID originale nero rilevato - è il turno del giocatore NERO
 [TURN INDICATOR] ✅ Classe current-player aggiunta a Player2 (NERO)
 [TURN INDICATOR] ✅ Classe current-turn aggiunta a player2-area per GLOW
 [TURN INDICATOR DEBUG] Player1 classes: player-name loaded
 [TURN INDICATOR DEBUG] Player2 classes: player-name loaded current-player
 [TURN INDICATOR DEBUG] Body ready-for-play: true
 [TURN INDICATOR] 🎯 INDICATORE ATTIVO su Player2: bruscolino
 [TURN INDICATOR] === FINE AGGIORNAMENTO ===
 [GAME STATE] Partita online - continuo con flusso completo per garantire drag and drop
 [GAME STATE] isFirstStateReceived: false
 [GAME STATE] shouldShowDiceAnimation: false
 [TURN PROTECTION] 🔒 MEMORIZZATO turno SOCKET ID ORIGINALE: BZ0EG04KJTrrcpBzAAAR
 [TURN PROTECTION] Protezione attiva per 10 secondi
 [DECK COUNTER DEBUG] gameState ricevuto con deckSize: 28
 === STATO TURNO RICEVUTO ===
 [TURN SYNC] currentPlayerId dal server: BZ0EG04KJTrrcpBzAAAR
 [TURN SYNC] myPlayerId locale: BZ0EG04KJTrrcpBzAAAR
 [TURN SYNC] window.myPlayerId (per mani): BZ0EG04KJTrrcpBzAAAR
 [TURN SYNC] mySocketId (per socket): 6x5D7qVor5EIyN4yAAAV
 [TURN SYNC] effectiveMyPlayerId (per mani): BZ0EG04KJTrrcpBzAAAR
 [TURN SYNC] effectiveTurnPlayerId (per turni): BZ0EG04KJTrrcpBzAAAR
 [TURN SYNC] state.mode: online
 [TURN SYNC] È il mio turno? (CORRETTO): true
 [TURN SYNC] playerColors: Object
 [TURN SYNC] playerNames: Object
 [TURN DEBUG] === ANALISI DETTAGLIATA TURNO ONLINE ===
 [TURN DEBUG] currentPlayerId dal server: BZ0EG04KJTrrcpBzAAAR
 [TURN DEBUG] mySocketId attuale: 6x5D7qVor5EIyN4yAAAV
 [TURN DEBUG] Confronto diretto: false
 [TURN DEBUG] Giocatori nello stato:
 [TURN DEBUG]   BZ0EG04KJTrrcpBzAAAR (black): è currentPlayer? true
 [TURN DEBUG]   ZqI2Xh9KZ6P4Xvw9AAAK (white): è currentPlayer? false
 [TURN DEBUG] isMyTurn dal game mode manager: false
 [TURN DEBUG] === FINE ANALISI ===
 ==========================
 [ONLINE GAME] Aggiornamento stato di gioco
 [ONLINE GAME] currentPlayerId BEFORE mapping: BZ0EG04KJTrrcpBzAAAR
 [ONLINE GAME MAPPING] === INIZIO MAPPING DEBUG ===
 [ONLINE GAME MAPPING] state.players keys: Array(2)
 [ONLINE GAME MAPPING] this.myPlayerId: BZ0EG04KJTrrcpBzAAAR
 [ONLINE GAME MAPPING] state.currentPlayerId: BZ0EG04KJTrrcpBzAAAR
 [ONLINE GAME MAPPING] state.originalCurrentPlayerId (prima): undefined
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: BZ0EG04KJTrrcpBzAAAR
 [ONLINE GAME MAPPING] this.opponentId: ZqI2Xh9KZ6P4Xvw9AAAK
 [ONLINE GAME MAPPING] state.currentPlayerId: BZ0EG04KJTrrcpBzAAAR
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [ONLINE GAME MAPPING] Io sono NERO, assegno: player1=opponent, player2=me
 [ONLINE GAME MAPPING] PRIMA trasformazione currentPlayerId: BZ0EG04KJTrrcpBzAAAR
 [ONLINE GAME MAPPING] Controllo: currentPlayerId === myPlayerId? true
 [ONLINE GAME MAPPING] Controllo: currentPlayerId === opponentId? false
 [ONLINE GAME MAPPING] È il MIO turno, mio colore: black -> assegno currentPlayerId = player2
 [ONLINE GAME MAPPING] DOPO trasformazione currentPlayerId: player2
 [ONLINE GAME] Player mapping:
 [ONLINE GAME] - Player 1 (white): ZqI2Xh9KZ6P4Xvw9AAAK
 [ONLINE GAME] - Player 2 (black): BZ0EG04KJTrrcpBzAAAR
 [ONLINE GAME] - Current player ID transformed: player2
 [ONLINE GAME] - Original current player ID (socket): BZ0EG04KJTrrcpBzAAAR
 [ONLINE GAME MAPPING] === FINE DEBUG MAPPING ===
 [ONLINE GAME] currentPlayerId AFTER mapping: player2
 [ONLINE GAME] originalCurrentPlayerId saved as: BZ0EG04KJTrrcpBzAAAR
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(4)
 [GAME STATE] player2 (black): Array(5)
 [PLAYER AREAS] Nomi definitivamente impostati, NON li tocco MAI
 [TURN INDICATOR] Aggiornamento solo indicatore turno
 [TURN INDICATOR] currentPlayerId ricevuto: player2
 [TURN INDICATOR] ✅ ID mappato player2 rilevato - è il turno del giocatore NERO
 [TURN INDICATOR] ✅ Classe current-player aggiunta a Player2 (NERO)
 [TURN INDICATOR] ✅ Classe current-turn aggiunta a player2-area per GLOW
 [TURN INDICATOR DEBUG] Player1 classes: player-name loaded
 [TURN INDICATOR DEBUG] Player2 classes: player-name loaded current-player
 [TURN INDICATOR DEBUG] Body ready-for-play: true
 [TURN INDICATOR] 🎯 INDICATORE ATTIVO su Player2: bruscolino
 [TURN INDICATOR] === FINE AGGIORNAMENTO ===
 [PERMANENT NAMES DEBUG] Server playerNames: {"BZ0EG04KJTrrcpBzAAAR":"bruscolino","ZqI2Xh9KZ6P4Xvw9AAAK":"giggio"}
 [PERMANENT NAMES DEBUG] Attuali permanentPlayerNames: {"BZ0EG04KJTrrcpBzAAAR":"bruscolino","ZqI2Xh9KZ6P4Xvw9AAAK":"giggio"}
 [PERMANENT NAMES] Mantenuto nome esistente per BZ0EG04KJTrrcpBzAAAR: bruscolino
 [PERMANENT NAMES] Mantenuto nome esistente per ZqI2Xh9KZ6P4Xvw9AAAK: giggio
 [PERMANENT NAMES] Nomi permanenti finali: {"BZ0EG04KJTrrcpBzAAAR":"bruscolino","ZqI2Xh9KZ6P4Xvw9AAAK":"giggio"}
 [PERMANENT COLORS DEBUG] Server playerColors: {"BZ0EG04KJTrrcpBzAAAR":"black","ZqI2Xh9KZ6P4Xvw9AAAK":"white"}
 [PERMANENT COLORS DEBUG] Attuali permanentPlayerColors: {"BZ0EG04KJTrrcpBzAAAR":"black","ZqI2Xh9KZ6P4Xvw9AAAK":"white"}
 [PERMANENT COLORS] Confermato colore per BZ0EG04KJTrrcpBzAAAR: black
 [PERMANENT COLORS] Confermato colore per ZqI2Xh9KZ6P4Xvw9AAAK: white
 [PERMANENT COLORS] Colori permanenti finali: {"BZ0EG04KJTrrcpBzAAAR":"black","ZqI2Xh9KZ6P4Xvw9AAAK":"white"}
 [EARLY PLAYER2 MONITOR] Observer installato il prima possibile, stato attuale: bruscolino
 [EARLY RESTORE] Controllo nomi: Object
 [GAME STATE DEBUG] Entrando nel ramo else if per updateGameUI - isOnline: true
 [CARD PROTECTION] 🛡️ Protezione POST-MAPPING (normale) attivata
 [CARD PROTECTION] 🔍 Inizio validazione stato di gioco MIGLIORATA...
 [CARD PROTECTION] 🔍 Modalità: online
 [CARD PROTECTION] 🔍 Giocatori presenti: Array(2)
 [CARD PROTECTION] 📋 Carte sul tabellone: 2
 [CARD PROTECTION] 📋 Paper-2 (2 di Paper) in b6
 [CARD PROTECTION] 📋 Rock-8 (8 di Rock) in c6
 [CARD PROTECTION] 👤 Controllo giocatore player1:
 [CARD PROTECTION] 👤 - Colore: white
 [CARD PROTECTION] 👤 - Carte in mano: 4
 [CARD PROTECTION] 👤 - Dettaglio carte: Array(4)
 [CARD PROTECTION] 👤 Controllo giocatore player2:
 [CARD PROTECTION] 👤 - Colore: black
 [CARD PROTECTION] 👤 - Carte in mano: 5
 [CARD PROTECTION] 👤 - Dettaglio carte: Array(5)
 [CARD PROTECTION] ✅ Stato consistente - nessuna duplicazione rilevata
 [GAME STATE DEBUG] Chiamando updateGameUI dal ramo else if
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: 0
 [ADVANTAGE] Percentuale finale: 50.0%
 [DEBUG] Vantaggio calcolato: 50%
 [HISTORY] Aggiunta mossa #1 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Modalità online - originalTurnPlayerId: BZ0EG04KJTrrcpBzAAAR window.myPlayerId: BZ0EG04KJTrrcpBzAAAR isMyTurn: true
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] INIZIO modalità online - player1/player2 già mappati da online-game.js
 [ONLINE UI] Identificato: io sono PLAYER2 (nero)
 [ONLINE UI] PROTEZIONE TURNO ATTIVA - Uso ID protetto: BZ0EG04KJTrrcpBzAAAR
 [ONLINE UI] Debug turno DETTAGLIATO:
 [ONLINE UI] - state.originalCurrentPlayerId: BZ0EG04KJTrrcpBzAAAR tipo: string
 [ONLINE UI] - window.initialTurnPlayerId (protetto): BZ0EG04KJTrrcpBzAAAR
 [ONLINE UI] - window.turnProtectionActive: true
 [ONLINE UI] - effectiveCurrentPlayerId (usato): BZ0EG04KJTrrcpBzAAAR
 [ONLINE UI] - window.myPlayerId: BZ0EG04KJTrrcpBzAAAR tipo: string
 [ONLINE UI] - Confronto diretto (===): true
 [ONLINE UI] - Confronto == : true
 [ONLINE UI] - effectiveCurrentPlayerId presente?: true
 [ONLINE UI] - window.myPlayerId presente?: true
 [ONLINE UI] - isMyTurn: true
 [ONLINE UI] - isPlayer1Local: false isPlayer1Turn: false
 [ONLINE UI] - isPlayer2Local: true isPlayer2Turn: true
 [NAMES PROTECTION] renderHand intercettato, cards: 4
 [RENDER HAND] Rendering 4 cards to player1-hand, isClickable: false
 [RENDER HAND] Cards: Array(4)
 [BRUSCOLINO RENDER DEBUG] Rendering mano player1-hand, isClickable: false
 [HAND DEBUG] player1-hand: isClickable=false, handLength=4, isOpponentHand=true
 [OPPONENT HAND] player1-hand: previousSize=null, currentSize=4, hasPlayedCard=false
 [CARD DRAG] NON trascinabile carta Rock-2 in player1-hand - isClickable:false, suit:Rock, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Rock-2 in player1-hand - motivo: isClickable=false, suit=Rock, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [CARD DRAG] NON trascinabile carta Paper-9 in player1-hand - isClickable:false, suit:Paper, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Paper-9 in player1-hand - motivo: isClickable=false, suit=Paper, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [CARD DRAG] NON trascinabile carta Paper-Q in player1-hand - isClickable:false, suit:Paper, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Paper-Q in player1-hand - motivo: isClickable=false, suit=Paper, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [CARD DRAG] NON trascinabile carta Paper-7 in player1-hand - isClickable:false, suit:Paper, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Paper-7 in player1-hand - motivo: isClickable=false, suit=Paper, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [NAMES PROTECTION] renderHand intercettato, cards: 5
 [RENDER HAND] Rendering 5 cards to player2-hand, isClickable: true
 [RENDER HAND] Cards: Array(5)
 [BRUSCOLINO RENDER DEBUG] Rendering mano player2-hand, isClickable: true
 [HAND DEBUG] player2-hand: isClickable=true, handLength=5, isOpponentHand=false
 [CARD DRAG] Rendendo trascinabile carta Rock-5 in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Rock-5 in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [CARD DRAG] Rendendo trascinabile carta Paper-10 in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Paper-10 in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [CARD DRAG] Rendendo trascinabile carta Paper-K in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Paper-K in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [CARD DRAG] Rendendo trascinabile carta Paper-A in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Paper-A in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [CARD DRAG] Rendendo trascinabile carta Rock-4 in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Rock-4 in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [BOARD DATA ONLY] Aggiornamento carte tabellone
 [BOARD DATA ONLY] Parametri ricevuti: Object
 [BOARD DATA ONLY] Inizio ciclo aggiornamento carte per 36 posizioni
 [BOARD DATA ONLY] Processando carta in b6: Object
 [BOARD DATA ONLY] Creando nuova carta in b6 per: Object
 [BOARD DATA ONLY] Carta aggiunta al DOM in b6
 [BOARD DATA ONLY] Processando carta in c6: Object
 [BOARD DATA ONLY] Creando nuova carta in c6 per: Object
 [BOARD DATA ONLY] Carta aggiunta al DOM in c6
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
 [DECK COUNTER DEBUG] updateGameUI con state.deckSize: 28 (tipo: number)
 [DECK COUNTER DEBUG] Chiamando renderDeck con validDeckSize: 28
 [DECK COUNTER DEBUG] renderDeck chiamato con remainingCards: 28
 [DECK COUNTER DEBUG] Valori: oldCounterValue=39, newCounterValue=28
 [DECK COUNTER DEBUG] Contatore aggiornato a: 28
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [CONSISTENCY CHECK] 🔍 Verifica consistenza carte tra tabellone e mani...
 [CONSISTENCY CHECK] 📋 Carte sul tabellone (2): Array(2)
 [CONSISTENCY CHECK] 👤 Controllo mano di player1 (white): 4 carte
 [CONSISTENCY CHECK] 👤 Carte in mano: Array(4)
 [CONSISTENCY CHECK] 👤 Controllo mano di player2 (black): 5 carte
 [CONSISTENCY CHECK] 👤 Carte in mano: Array(5)
 [CONSISTENCY CHECK] ✅ Nessuna inconsistenza rilevata
 [CONSISTENCY FINAL] Verifica finale consistenza dopo elaborazione...
 [CONSISTENCY FINAL] ✅ Stato finale consistente
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TURN TIMER DEBUG] === ANALISI CONTROLLO TURNO ===
 [TURN TIMER DEBUG] gameState.currentPlayerId: player2
 [TURN TIMER DEBUG] gameState.myPlayerId: 6x5D7qVor5EIyN4yAAAV
 [TURN TIMER DEBUG] window.myPlayerId: BZ0EG04KJTrrcpBzAAAR
 [TURN TIMER DEBUG] window.socket?.id: 6x5D7qVor5EIyN4yAAAV
 [TURN TIMER DEBUG] mySocketId: 6x5D7qVor5EIyN4yAAAV
 [TURN TIMER DEBUG] isMyTurn (CORRETTO): false
 [TURN TIMER DEBUG] === FINE ANALISI ===
 [TIMER] Avviato timer totale per giocatore 2
 [GAMESTATE] Rating giocatore 2 aggiornato: 1000
 [PSN DEBUG] Inizio registerMove: card={"suit":"Rock","value":"8","isNeutral":false}, position=c6 (mossa), color=white, gainedVertexControl=undefined, cardsRemaining=undefined, state.currentTurn (prima)=0, state.currentPlayer=white
 [PSN DEBUG RegisterMove Pre-Check] state.pendingVertexControl attuale: undefined
 [PSN DEBUG RegisterMove Pre-Check] state.pendingVertexControl è null o undefined.
 [PSN DEBUG] Dentro if(isWhite): state.currentTurn era 0, ora è 1
 [PSN DEBUG RegisterMove Pending] state.pendingVertexControl è vuoto o non definito al momento del check.
 [PSN DEBUG] Oggetto 'move' creato (dopo check pending): {"turnNumber":1,"isWhite":true,"card":{"suit":"Rock","value":"8","isNeutral":false},"position":"c6","gainedVertexControl":false,"timestamp":"2025-06-26T01:15:35.509Z"}
 [PSN] Gioco iniziato - prima mossa registrata
 [PSN] Prima mossa rilevata, cambio a vista Notazioni.
 [PSN] Mossa registrata (con debug): Turno 1, Bianco - Rock 8 su c6. Prossimo giocatore: black. StateTurn: 1
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [VISIBILITY] Stato di visibilità cambiato: visible
 [VISIBILITY] Notificato al server: browser tornato visibile
 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
 [VISIBILITY] Eseguo pulizia cover cards bloccate...
 [VISIBILITY] Trovate 11 cover cards bloccate, le rimuovo
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Animazione carta iniziale già in corso in background
 [ANIM] Fine animazione movimento. Rimuovo cover e mostro carta reale.
 [ANIM] Utilizzo fallback setTimeout per animazione finale
 [SOCKET] Cambio di visibilità del client w4FQyhXUasfbOXMeAAAT: hidden
 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [ANIM] Fine animateInitialCardPlacement con fallback.
 [ANIMATION DEBUG] Verifica condizioni per animazione nomi: Object
 [ANIMATION] Animando con G1 (Bianco): giggio vs G2 (Nero): bruscolino
 [ANIMATION] Convenzione standard: Bianco in alto, Nero in basso
 [PLAYER NAMES ANIMATION] ===== FUNZIONE CHIAMATA =====
 [PLAYER NAMES ANIMATION] Avvio animazione nomi giocatori DIRETTA
 [PLAYER NAMES ANIMATION] Nomi forniti: giggio vs bruscolino
 [PLAYER NAMES ANIMATION] Usando nomi forniti direttamente: giggio vs bruscolino
 [PLAYER NAMES ANIMATION] Nomi giocatori finali: giggio vs bruscolino
 [PLAYER NAMES ANIMATION] Game board trovato: true
 [PLAYER NAMES ANIMATION] Game board dimensioni: DOMRect
 [AVATAR DEBUG] Cercando avatar per giggio per posizione top
 [AVATAR DEBUG] Livello determinato per giggio: Principiante
 [AVATAR DEBUG] Tentativo di caricamento avatar da: http://localhost:3000/img/avatar/Principiante.webp
 [AVATAR DEBUG] Avatar trovato nella cache: http://localhost:3000/img/avatar/Principiante.webp
 [AVATAR DEBUG] Cercando avatar per bruscolino per posizione bottom
 [AVATAR DEBUG] Livello determinato per bruscolino: Principiante
 [AVATAR DEBUG] Tentativo di caricamento avatar da: http://localhost:3000/img/avatar/Principiante.webp
 [AVATAR DEBUG] Avatar trovato nella cache: http://localhost:3000/img/avatar/Principiante.webp
 [PLAYER NAMES ANIMATION] Animazione creata dinamicamente e aggiunta al DOM
 [PLAYER NAMES ANIMATION] Container aggiunto al game-board: dynamic-names-animation
 [PLAYER NAMES ANIMATION] Game-board children count: 41
 [AVATAR DEBUG] Avatar player1 caricato con successo
 [AVATAR DEBUG] Avatar player2 caricato con successo
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PLAYER NAMES ANIMATION] Terminando animazione
 [PLAYER NAMES ANIMATION] Avvio fade-in immediato del game container
 [PLAYER NAMES ANIMATION] Fade-in avviato immediatamente
 [PLAYER NAMES ANIMATION] Animazione completata e rimossa
 [PLAYER NAMES ANIMATION] Fade-in completato
 [PLAYER NAMES ANIMATION] isSetupAnimating resettato a false alla fine reale dell'animazione
 [PLAYER NAMES ANIMATION] nameAnimationCompleted impostato a true - animazione nomi completata
 [PLAYER NAMES ANIMATION] Forzando re-rendering delle carte dopo reset isSetupAnimating...
 [SETUP RE-RENDER] Forzando re-rendering delle carte dopo fine setup...
 [SETUP RE-RENDER] Player player1 (white): isLocal=false, isMyTurn=true, clickable=false
 [SETUP RE-RENDER] originalCurrentPlayerId: BZ0EG04KJTrrcpBzAAAR, myPlayerId: BZ0EG04KJTrrcpBzAAAR
 [SETUP RE-RENDER] myColor: black, playerColor: white
 [SETUP RE-RENDER] Re-rendering mano white (player1), clickable: false
 [NAMES PROTECTION] renderHand intercettato, cards: 4
 [RENDER HAND] Rendering 4 cards to player1-hand, isClickable: false
 [RENDER HAND] Cards: Array(4)
 [BRUSCOLINO RENDER DEBUG] Rendering mano player1-hand, isClickable: false
 [HAND DEBUG] player1-hand: isClickable=false, handLength=4, isOpponentHand=true
 [OPPONENT HAND] player1-hand: previousSize=4, currentSize=4, hasPlayedCard=false
 [CARD DRAG] NON trascinabile carta Rock-2 in player1-hand - isClickable:false, suit:Rock, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Rock-2 in player1-hand - motivo: isClickable=false, suit=Rock, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [CARD DRAG] NON trascinabile carta Paper-9 in player1-hand - isClickable:false, suit:Paper, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Paper-9 in player1-hand - motivo: isClickable=false, suit=Paper, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [CARD DRAG] NON trascinabile carta Paper-Q in player1-hand - isClickable:false, suit:Paper, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Paper-Q in player1-hand - motivo: isClickable=false, suit=Paper, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [CARD DRAG] NON trascinabile carta Paper-7 in player1-hand - isClickable:false, suit:Paper, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Paper-7 in player1-hand - motivo: isClickable=false, suit=Paper, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [SETUP RE-RENDER] Player player2 (black): isLocal=true, isMyTurn=true, clickable=true
 [SETUP RE-RENDER] originalCurrentPlayerId: BZ0EG04KJTrrcpBzAAAR, myPlayerId: BZ0EG04KJTrrcpBzAAAR
 [SETUP RE-RENDER] myColor: black, playerColor: black
 [SETUP RE-RENDER] Re-rendering mano black (player2), clickable: true
 [NAMES PROTECTION] renderHand intercettato, cards: 5
 [RENDER HAND] Rendering 5 cards to player2-hand, isClickable: true
 [RENDER HAND] Cards: Array(5)
 [BRUSCOLINO RENDER DEBUG] Rendering mano player2-hand, isClickable: true
 [HAND DEBUG] player2-hand: isClickable=true, handLength=5, isOpponentHand=false
 [CARD DRAG] Rendendo trascinabile carta Rock-5 in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Rock-5 in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [CARD DRAG] Rendendo trascinabile carta Paper-10 in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Paper-10 in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [CARD DRAG] Rendendo trascinabile carta Paper-K in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Paper-K in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [CARD DRAG] Rendendo trascinabile carta Paper-A in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Paper-A in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [CARD DRAG] Rendendo trascinabile carta Rock-4 in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Rock-4 in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [SETUP RE-RENDER] Re-rendering completato
 [ANIMATION] Animazioni completate e marcate per gameId: HTCSFZ
 [ANIMATION] isSetupAnimating sarà resettato da animatePlayerNames() alla fine reale dell'animazione
 [ANIMATION] Chiamando handleGameState con animazioni completate
 [DECK COUNTER DEBUG] gameState ricevuto con deckSize: 28
 === STATO TURNO RICEVUTO ===
 [TURN SYNC] currentPlayerId dal server: ZqI2Xh9KZ6P4Xvw9AAAK
 [TURN SYNC] myPlayerId locale: BZ0EG04KJTrrcpBzAAAR
 [TURN SYNC] window.myPlayerId (per mani): BZ0EG04KJTrrcpBzAAAR
 [TURN SYNC] mySocketId (per socket): 6x5D7qVor5EIyN4yAAAV
 [TURN SYNC] effectiveMyPlayerId (per mani): BZ0EG04KJTrrcpBzAAAR
 [TURN SYNC] effectiveTurnPlayerId (per turni): BZ0EG04KJTrrcpBzAAAR
 [TURN SYNC] state.mode: online
 [TURN SYNC] È il mio turno? (CORRETTO): false
 [TURN SYNC] playerColors: Object
 [TURN SYNC] playerNames: Object
 [TURN DEBUG] === ANALISI DETTAGLIATA TURNO ONLINE ===
 [TURN DEBUG] currentPlayerId dal server: ZqI2Xh9KZ6P4Xvw9AAAK
 [TURN DEBUG] mySocketId attuale: 6x5D7qVor5EIyN4yAAAV
 [TURN DEBUG] Confronto diretto: false
 [TURN DEBUG] Giocatori nello stato:
 [TURN DEBUG]   BZ0EG04KJTrrcpBzAAAR (black): è currentPlayer? false
 [TURN DEBUG]   ZqI2Xh9KZ6P4Xvw9AAAK (white): è currentPlayer? true
 [TURN DEBUG] isMyTurn dal game mode manager: true
 [TURN DEBUG] === FINE ANALISI ===
 ==========================
 [ONLINE GAME] Aggiornamento stato di gioco
 [ONLINE GAME] currentPlayerId BEFORE mapping: ZqI2Xh9KZ6P4Xvw9AAAK
 [ONLINE GAME MAPPING] === INIZIO MAPPING DEBUG ===
 [ONLINE GAME MAPPING] state.players keys: Array(2)
 [ONLINE GAME MAPPING] this.myPlayerId: BZ0EG04KJTrrcpBzAAAR
 [ONLINE GAME MAPPING] state.currentPlayerId: ZqI2Xh9KZ6P4Xvw9AAAK
 [ONLINE GAME MAPPING] state.originalCurrentPlayerId (prima): undefined
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: BZ0EG04KJTrrcpBzAAAR
 [ONLINE GAME MAPPING] this.opponentId: ZqI2Xh9KZ6P4Xvw9AAAK
 [ONLINE GAME MAPPING] state.currentPlayerId: ZqI2Xh9KZ6P4Xvw9AAAK
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [ONLINE GAME MAPPING] Io sono NERO, assegno: player1=opponent, player2=me
 [ONLINE GAME MAPPING] PRIMA trasformazione currentPlayerId: ZqI2Xh9KZ6P4Xvw9AAAK
 [ONLINE GAME MAPPING] Controllo: currentPlayerId === myPlayerId? false
 [ONLINE GAME MAPPING] Controllo: currentPlayerId === opponentId? true
 [ONLINE GAME MAPPING] È il turno dell'AVVERSARIO, suo colore: white -> assegno currentPlayerId = player1
 [ONLINE GAME MAPPING] DOPO trasformazione currentPlayerId: player1
 [ONLINE GAME] Player mapping:
 [ONLINE GAME] - Player 1 (white): ZqI2Xh9KZ6P4Xvw9AAAK
 [ONLINE GAME] - Player 2 (black): BZ0EG04KJTrrcpBzAAAR
 [ONLINE GAME] - Current player ID transformed: player1
 [ONLINE GAME] - Original current player ID (socket): ZqI2Xh9KZ6P4Xvw9AAAK
 [ONLINE GAME MAPPING] === FINE DEBUG MAPPING ===
 [ONLINE GAME] currentPlayerId AFTER mapping: player1
 [ONLINE GAME] originalCurrentPlayerId saved as: ZqI2Xh9KZ6P4Xvw9AAAK
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(5)
 [GAME STATE] player2 (black): Array(5)
 [PLAYER AREAS] Nomi definitivamente impostati, NON li tocco MAI
 [TURN INDICATOR] Aggiornamento solo indicatore turno
 [TURN INDICATOR] currentPlayerId ricevuto: player1
 [TURN INDICATOR] ✅ ID mappato player1 rilevato - è il turno del giocatore BIANCO
 [TURN INDICATOR] ✅ Classe current-player aggiunta a Player1 (BIANCO)
 [TURN INDICATOR] ✅ Classe current-turn aggiunta a player1-area per GLOW
 [TURN INDICATOR DEBUG] Player1 classes: player-name loaded current-player
 [TURN INDICATOR DEBUG] Player2 classes: player-name loaded
 [TURN INDICATOR DEBUG] Body ready-for-play: true
 [TURN INDICATOR] 🎯 INDICATORE ATTIVO su Player1: giggio
 [TURN INDICATOR] === FINE AGGIORNAMENTO ===
 [PERMANENT NAMES DEBUG] Server playerNames: {"BZ0EG04KJTrrcpBzAAAR":"bruscolino","ZqI2Xh9KZ6P4Xvw9AAAK":"giggio"}
 [PERMANENT NAMES DEBUG] Attuali permanentPlayerNames: {"BZ0EG04KJTrrcpBzAAAR":"bruscolino","ZqI2Xh9KZ6P4Xvw9AAAK":"giggio"}
 [PERMANENT NAMES] Mantenuto nome esistente per BZ0EG04KJTrrcpBzAAAR: bruscolino
 [PERMANENT NAMES] Mantenuto nome esistente per ZqI2Xh9KZ6P4Xvw9AAAK: giggio
 [PERMANENT NAMES] Nomi permanenti finali: {"BZ0EG04KJTrrcpBzAAAR":"bruscolino","ZqI2Xh9KZ6P4Xvw9AAAK":"giggio"}
 [PERMANENT COLORS DEBUG] Server playerColors: {"BZ0EG04KJTrrcpBzAAAR":"black","ZqI2Xh9KZ6P4Xvw9AAAK":"white"}
 [PERMANENT COLORS DEBUG] Attuali permanentPlayerColors: {"BZ0EG04KJTrrcpBzAAAR":"black","ZqI2Xh9KZ6P4Xvw9AAAK":"white"}
 [PERMANENT COLORS] Confermato colore per BZ0EG04KJTrrcpBzAAAR: black
 [PERMANENT COLORS] Confermato colore per ZqI2Xh9KZ6P4Xvw9AAAK: white
 [PERMANENT COLORS] Colori permanenti finali: {"BZ0EG04KJTrrcpBzAAAR":"black","ZqI2Xh9KZ6P4Xvw9AAAK":"white"}
 [GAME STATE DEBUG] Entrando nel ramo else if per updateGameUI - isOnline: true
 [CARD PROTECTION] 🛡️ Protezione POST-MAPPING (normale) attivata
 [CARD PROTECTION] 🔍 Inizio validazione stato di gioco MIGLIORATA...
 [CARD PROTECTION] 🔍 Modalità: online
 [CARD PROTECTION] 🔍 Giocatori presenti: Array(2)
 [CARD PROTECTION] 📋 Carte sul tabellone: 1
 [CARD PROTECTION] 📋 Rock-8 (8 di Rock) in c6
 [CARD PROTECTION] 👤 Controllo giocatore player1:
 [CARD PROTECTION] 👤 - Colore: white
 [CARD PROTECTION] 👤 - Carte in mano: 5
 [CARD PROTECTION] 👤 - Dettaglio carte: Array(5)
 [CARD PROTECTION] 👤 Controllo giocatore player2:
 [CARD PROTECTION] 👤 - Colore: black
 [CARD PROTECTION] 👤 - Carte in mano: 5
 [CARD PROTECTION] 👤 - Dettaglio carte: Array(5)
 [CARD PROTECTION] ✅ Stato consistente - nessuna duplicazione rilevata
 [GAME STATE DEBUG] Chiamando updateGameUI dal ramo else if
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: 0
 [ADVANTAGE] Percentuale finale: 50.0%
 [DEBUG] Vantaggio calcolato: 50%
 [HISTORY] Aggiunta mossa #2 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Modalità online - originalTurnPlayerId: ZqI2Xh9KZ6P4Xvw9AAAK window.myPlayerId: BZ0EG04KJTrrcpBzAAAR isMyTurn: false
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [OPPONENT DRAW DETECTED] L'avversario ha pescato 1 carte
 [OPPONENT ANIMATION BLOCKED] Animazione carte avversario disabilitata per modalità multiplayer
 [GAME MESSAGE] Mostrato messaggio nell'interfaccia: "Avversario ha pescato 1 carta!"
 [ONLINE UI] INIZIO modalità online - player1/player2 già mappati da online-game.js
 [ONLINE UI] Identificato: io sono PLAYER2 (nero)
 [ONLINE UI] PROTEZIONE TURNO ATTIVA - Uso ID protetto: BZ0EG04KJTrrcpBzAAAR
 [ONLINE UI] Debug turno DETTAGLIATO:
 [ONLINE UI] - state.originalCurrentPlayerId: ZqI2Xh9KZ6P4Xvw9AAAK tipo: string
 [ONLINE UI] - window.initialTurnPlayerId (protetto): BZ0EG04KJTrrcpBzAAAR
 [ONLINE UI] - window.turnProtectionActive: true
 [ONLINE UI] - effectiveCurrentPlayerId (usato): BZ0EG04KJTrrcpBzAAAR
 [ONLINE UI] - window.myPlayerId: BZ0EG04KJTrrcpBzAAAR tipo: string
 [ONLINE UI] - Confronto diretto (===): true
 [ONLINE UI] - Confronto == : true
 [ONLINE UI] - effectiveCurrentPlayerId presente?: true
 [ONLINE UI] - window.myPlayerId presente?: true
 [ONLINE UI] - isMyTurn: true
 [ONLINE UI] - isPlayer1Local: false isPlayer1Turn: false
 [ONLINE UI] - isPlayer2Local: true isPlayer2Turn: true
 [NAMES PROTECTION] renderHand intercettato, cards: 5
 [RENDER HAND] Rendering 5 cards to player1-hand, isClickable: false
 [RENDER HAND] Cards: Array(5)
 [BRUSCOLINO RENDER DEBUG] Rendering mano player1-hand, isClickable: false
 [HAND DEBUG] player1-hand: isClickable=false, handLength=5, isOpponentHand=true
 [OPPONENT HAND] player1-hand: previousSize=4, currentSize=5, hasPlayedCard=false
 [CARD DRAG] NON trascinabile carta Rock-2 in player1-hand - isClickable:false, suit:Rock, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Rock-2 in player1-hand - motivo: isClickable=false, suit=Rock, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [CARD DRAG] NON trascinabile carta Paper-9 in player1-hand - isClickable:false, suit:Paper, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Paper-9 in player1-hand - motivo: isClickable=false, suit=Paper, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [CARD DRAG] NON trascinabile carta Paper-Q in player1-hand - isClickable:false, suit:Paper, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Paper-Q in player1-hand - motivo: isClickable=false, suit=Paper, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [CARD DRAG] NON trascinabile carta Paper-2 in player1-hand - isClickable:false, suit:Paper, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Paper-2 in player1-hand - motivo: isClickable=false, suit=Paper, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [CARD DRAG] NON trascinabile carta Paper-7 in player1-hand - isClickable:false, suit:Paper, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Paper-7 in player1-hand - motivo: isClickable=false, suit=Paper, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [NAMES PROTECTION] renderHand intercettato, cards: 5
 [RENDER HAND] Rendering 5 cards to player2-hand, isClickable: true
 [RENDER HAND] Cards: Array(5)
 [BRUSCOLINO RENDER DEBUG] Rendering mano player2-hand, isClickable: true
 [HAND DEBUG] player2-hand: isClickable=true, handLength=5, isOpponentHand=false
 [CARD DRAG] Rendendo trascinabile carta Rock-5 in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Rock-5 in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [CARD DRAG] Rendendo trascinabile carta Paper-10 in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Paper-10 in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [CARD DRAG] Rendendo trascinabile carta Paper-K in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Paper-K in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [CARD DRAG] Rendendo trascinabile carta Paper-A in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Paper-A in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [CARD DRAG] Rendendo trascinabile carta Rock-4 in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Rock-4 in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [BOARD DATA ONLY] Aggiornamento carte tabellone
 [BOARD DATA ONLY] Parametri ricevuti: Object
 [BOARD DATA ONLY] Inizio ciclo aggiornamento carte per 36 posizioni
 [BOARD DATA ONLY] Processando carta in c6: Object
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
 [DECK COUNTER DEBUG] updateGameUI con state.deckSize: 28 (tipo: number)
 [DECK COUNTER DEBUG] Chiamando renderDeck con validDeckSize: 28
 [DECK COUNTER DEBUG] renderDeck chiamato con remainingCards: 28
 [DECK COUNTER DEBUG] Valori: oldCounterValue=28, newCounterValue=28
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [CONSISTENCY CHECK] 🔍 Verifica consistenza carte tra tabellone e mani...
 [CONSISTENCY CHECK] 📋 Carte sul tabellone (1): Array(1)
 [CONSISTENCY CHECK] 👤 Controllo mano di player1 (white): 5 carte
 [CONSISTENCY CHECK] 👤 Carte in mano: Array(5)
 [CONSISTENCY CHECK] 👤 Controllo mano di player2 (black): 5 carte
 [CONSISTENCY CHECK] 👤 Carte in mano: Array(5)
 [CONSISTENCY CHECK] ✅ Nessuna inconsistenza rilevata
 [CONSISTENCY FINAL] Verifica finale consistenza dopo elaborazione...
 [CONSISTENCY FINAL] ✅ Stato finale consistente
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TURN TIMER DEBUG] === ANALISI CONTROLLO TURNO ===
 [TURN TIMER DEBUG] gameState.currentPlayerId: player1
 [TURN TIMER DEBUG] gameState.myPlayerId: BZ0EG04KJTrrcpBzAAAR
 [TURN TIMER DEBUG] window.myPlayerId: BZ0EG04KJTrrcpBzAAAR
 [TURN TIMER DEBUG] window.socket?.id: 6x5D7qVor5EIyN4yAAAV
 [TURN TIMER DEBUG] mySocketId: 6x5D7qVor5EIyN4yAAAV
 [TURN TIMER DEBUG] isMyTurn (CORRETTO): false
 [TURN TIMER DEBUG] === FINE ANALISI ===
 [GAMESTATE] Rating giocatore 2 aggiornato: 1000
 [TURN PROTECTION] ⚠️  CAMBIO TURNO RILEVATO durante protezione!
 [TURN PROTECTION] Ripristino da player1 a BZ0EG04KJTrrcpBzAAAR
 [TURN PROTECTION] Tempo rimanente: 3 secondi
 [TURN PROTECTION] ✅ Turno ripristinato
 [DECK COUNTER DEBUG] gameState ricevuto con deckSize: 28
 === STATO TURNO RICEVUTO ===
 [TURN SYNC] currentPlayerId dal server: BZ0EG04KJTrrcpBzAAAR
 [TURN SYNC] myPlayerId locale: BZ0EG04KJTrrcpBzAAAR
 [TURN SYNC] window.myPlayerId (per mani): BZ0EG04KJTrrcpBzAAAR
 [TURN SYNC] mySocketId (per socket): 6x5D7qVor5EIyN4yAAAV
 [TURN SYNC] effectiveMyPlayerId (per mani): BZ0EG04KJTrrcpBzAAAR
 [TURN SYNC] effectiveTurnPlayerId (per turni): BZ0EG04KJTrrcpBzAAAR
 [TURN SYNC] state.mode: online
 [TURN SYNC] È il mio turno? (CORRETTO): true
 [TURN SYNC] playerColors: Object
 [TURN SYNC] playerNames: Object
 [TURN DEBUG] === ANALISI DETTAGLIATA TURNO ONLINE ===
script.js:11581 [TURN DEBUG] currentPlayerId dal server: BZ0EG04KJTrrcpBzAAAR
script.js:11582 [TURN DEBUG] mySocketId attuale: 6x5D7qVor5EIyN4yAAAV
script.js:11583 [TURN DEBUG] Confronto diretto: false
script.js:11587 [TURN DEBUG] Giocatori nello stato:
script.js:11590 [TURN DEBUG]   player1 (white): è currentPlayer? false
script.js:11590 [TURN DEBUG]   player2 (black): è currentPlayer? false
script.js:11597 [TURN DEBUG] isMyTurn dal game mode manager: false
script.js:11600 [TURN DEBUG] === FINE ANALISI ===
script.js:11603 ==========================
online-game.js:71 [ONLINE GAME] Aggiornamento stato di gioco
online-game.js:72 [ONLINE GAME] currentPlayerId BEFORE mapping: BZ0EG04KJTrrcpBzAAAR
online-game.js:93 [ONLINE GAME MAPPING] === INIZIO MAPPING DEBUG ===
online-game.js:94 [ONLINE GAME MAPPING] state.players keys: Array(2)
online-game.js:95 [ONLINE GAME MAPPING] this.myPlayerId: BZ0EG04KJTrrcpBzAAAR
online-game.js:96 [ONLINE GAME MAPPING] state.currentPlayerId: BZ0EG04KJTrrcpBzAAAR
online-game.js:97 [ONLINE GAME MAPPING] state.originalCurrentPlayerId (prima): ZqI2Xh9KZ6P4Xvw9AAAK
online-game.js:101 [ONLINE GAME MAPPING] Mapping già fatto, skip
online-game.js:102 [ONLINE GAME MAPPING] player1.id: ZqI2Xh9KZ6P4Xvw9AAAK color: white
online-game.js:103 [ONLINE GAME MAPPING] player2.id: BZ0EG04KJTrrcpBzAAAR color: black
online-game.js:104 [ONLINE GAME MAPPING] Controllo originalCurrentPlayerId...
online-game.js:138 [ONLINE GAME MAPPING] originalCurrentPlayerId finale: ZqI2Xh9KZ6P4Xvw9AAAK
online-game.js:139 [ONLINE GAME MAPPING] === FINE MAPPING SKIP ===
online-game.js:80 [ONLINE GAME] currentPlayerId AFTER mapping: BZ0EG04KJTrrcpBzAAAR
online-game.js:81 [ONLINE GAME] originalCurrentPlayerId saved as: ZqI2Xh9KZ6P4Xvw9AAAK
script.js:11617 [GAME STATE] Stato mani dopo aggiornamento:
script.js:11620 [GAME STATE] player1 (white): Array(5)
script.js:11620 [GAME STATE] player2 (black): Array(5)
script.js:13451 [PLAYER AREAS] Nomi definitivamente impostati, NON li tocco MAI
script.js:13324 [TURN INDICATOR] Aggiornamento solo indicatore turno
script.js:13325 [TURN INDICATOR] currentPlayerId ricevuto: BZ0EG04KJTrrcpBzAAAR
script.js:13364 [TURN INDICATOR] Fallback - whitePlayerId: ZqI2Xh9KZ6P4Xvw9AAAK blackPlayerId: BZ0EG04KJTrrcpBzAAAR
script.js:13370 [TURN INDICATOR] ✅ ID originale nero rilevato - è il turno del giocatore NERO
script.js:13396 [TURN INDICATOR] ✅ Classe current-player aggiunta a Player2 (NERO)
script.js:13397 [TURN INDICATOR] ✅ Classe current-turn aggiunta a player2-area per GLOW
script.js:13407 [TURN INDICATOR DEBUG] Player1 classes: player-name loaded
script.js:13408 [TURN INDICATOR DEBUG] Player2 classes: player-name loaded current-player
script.js:13409 [TURN INDICATOR DEBUG] Body ready-for-play: true
script.js:13418 [TURN INDICATOR] 🎯 INDICATORE ATTIVO su Player2: bruscolino
script.js:13424 [TURN INDICATOR] === FINE AGGIORNAMENTO ===
script.js:11813 [PERMANENT NAMES DEBUG] Server playerNames: {"BZ0EG04KJTrrcpBzAAAR":"bruscolino","ZqI2Xh9KZ6P4Xvw9AAAK":"giggio"}
script.js:11814 [PERMANENT NAMES DEBUG] Attuali permanentPlayerNames: {"BZ0EG04KJTrrcpBzAAAR":"bruscolino","ZqI2Xh9KZ6P4Xvw9AAAK":"giggio"}
script.js:11826 [PERMANENT NAMES] Mantenuto nome esistente per BZ0EG04KJTrrcpBzAAAR: bruscolino
script.js:11826 [PERMANENT NAMES] Mantenuto nome esistente per ZqI2Xh9KZ6P4Xvw9AAAK: giggio
script.js:11830 [PERMANENT NAMES] Nomi permanenti finali: {"BZ0EG04KJTrrcpBzAAAR":"bruscolino","ZqI2Xh9KZ6P4Xvw9AAAK":"giggio"}
script.js:11833 [PERMANENT COLORS DEBUG] Server playerColors: {"BZ0EG04KJTrrcpBzAAAR":"black","ZqI2Xh9KZ6P4Xvw9AAAK":"white"}
script.js:11834 [PERMANENT COLORS DEBUG] Attuali permanentPlayerColors: {"BZ0EG04KJTrrcpBzAAAR":"black","ZqI2Xh9KZ6P4Xvw9AAAK":"white"}
script.js:11848 [PERMANENT COLORS] Confermato colore per BZ0EG04KJTrrcpBzAAAR: black
script.js:11848 [PERMANENT COLORS] Confermato colore per ZqI2Xh9KZ6P4Xvw9AAAK: white
script.js:11852 [PERMANENT COLORS] Colori permanenti finali: {"BZ0EG04KJTrrcpBzAAAR":"black","ZqI2Xh9KZ6P4Xvw9AAAK":"white"}
script.js:12402 [GAME STATE DEBUG] Entrando nel ramo else if per updateGameUI - isOnline: true
script.js:12411 [CARD PROTECTION] 🛡️ Protezione POST-MAPPING (normale) attivata
script.js:10645 [CARD PROTECTION] 🔍 Inizio validazione stato di gioco MIGLIORATA...
script.js:10646 [CARD PROTECTION] 🔍 Modalità: online
script.js:10647 [CARD PROTECTION] 🔍 Giocatori presenti: Array(2)
script.js:10665 [CARD PROTECTION] 📋 Carte sul tabellone: 1
script.js:10667 [CARD PROTECTION] 📋 Rock-8 (8 di Rock) in c6
script.js:10681 [CARD PROTECTION] 👤 Controllo giocatore player1:
script.js:10682 [CARD PROTECTION] 👤 - Colore: white
script.js:10683 [CARD PROTECTION] 👤 - Carte in mano: 5
script.js:10684 [CARD PROTECTION] 👤 - Dettaglio carte: Array(5)
script.js:10681 [CARD PROTECTION] 👤 Controllo giocatore player2:
script.js:10682 [CARD PROTECTION] 👤 - Colore: black
script.js:10683 [CARD PROTECTION] 👤 - Carte in mano: 5
script.js:10684 [CARD PROTECTION] 👤 - Dettaglio carte: Array(5)
script.js:10742 [CARD PROTECTION] ✅ Stato consistente - nessuna duplicazione rilevata
script.js:12417 [GAME STATE DEBUG] Chiamando updateGameUI dal ramo else if
script.js:12937 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
script.js:13003 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
script.js:13004 [ADVANTAGE] Valore vantaggio calcolato: 0
script.js:13060 [ADVANTAGE] Percentuale finale: 50.0%
script.js:13759 [DEBUG] Vantaggio calcolato: 50%
script.js:15230 Tentativo di forzare rendering rating...
script.js:15257 Ratings dopo init: Object
script.js:15266 Aggiornamento avatar player1: player1
script.js:15272 Aggiornamento avatar player2: player2
script.js:15582 [GIOCA TAB] Modalità online - originalTurnPlayerId: ZqI2Xh9KZ6P4Xvw9AAAK window.myPlayerId: BZ0EG04KJTrrcpBzAAAR isMyTurn: false
script.js:13831 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
script.js:13834 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
script.js:37 [PERSISTENCE] Salvataggio stato partita...
script.js:67 [PERSISTENCE] Stato salvato: Object
script.js:13956 [ONLINE UI] INIZIO modalità online - player1/player2 già mappati da online-game.js
script.js:13993 [ONLINE UI] Identificato: io sono PLAYER2 (nero)
script.js:14017 [ONLINE UI] PROTEZIONE TURNO ATTIVA - Uso ID protetto: BZ0EG04KJTrrcpBzAAAR
script.js:14025 [ONLINE UI] Debug turno DETTAGLIATO:
script.js:14026 [ONLINE UI] - state.originalCurrentPlayerId: ZqI2Xh9KZ6P4Xvw9AAAK tipo: string
script.js:14027 [ONLINE UI] - window.initialTurnPlayerId (protetto): BZ0EG04KJTrrcpBzAAAR
script.js:14028 [ONLINE UI] - window.turnProtectionActive: true
script.js:14029 [ONLINE UI] - effectiveCurrentPlayerId (usato): BZ0EG04KJTrrcpBzAAAR
script.js:14030 [ONLINE UI] - window.myPlayerId: BZ0EG04KJTrrcpBzAAAR tipo: string
script.js:14031 [ONLINE UI] - Confronto diretto (===): true
script.js:14032 [ONLINE UI] - Confronto == : true
script.js:14033 [ONLINE UI] - effectiveCurrentPlayerId presente?: true
script.js:14034 [ONLINE UI] - window.myPlayerId presente?: true
script.js:14035 [ONLINE UI] - isMyTurn: true
script.js:14036 [ONLINE UI] - isPlayer1Local: false isPlayer1Turn: false
script.js:14037 [ONLINE UI] - isPlayer2Local: true isPlayer2Turn: true
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 5
script.js:4588 [RENDER HAND] Rendering 5 cards to player1-hand, isClickable: false
script.js:4589 [RENDER HAND] Cards: Array(5)
script.js:4593 [BRUSCOLINO RENDER DEBUG] Rendering mano player1-hand, isClickable: false
script.js:4601 [HAND DEBUG] player1-hand: isClickable=false, handLength=5, isOpponentHand=true
script.js:4608 [OPPONENT HAND] player1-hand: previousSize=5, currentSize=5, hasPlayedCard=false
script.js:4768 [CARD DRAG] NON trascinabile carta Rock-2 in player1-hand - isClickable:false, suit:Rock, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
script.js:4772 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Rock-2 in player1-hand - motivo: isClickable=false, suit=Rock, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
script.js:4768 [CARD DRAG] NON trascinabile carta Paper-9 in player1-hand - isClickable:false, suit:Paper, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
script.js:4772 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Paper-9 in player1-hand - motivo: isClickable=false, suit=Paper, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
script.js:4768 [CARD DRAG] NON trascinabile carta Paper-Q in player1-hand - isClickable:false, suit:Paper, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
script.js:4772 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Paper-Q in player1-hand - motivo: isClickable=false, suit=Paper, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
script.js:4768 [CARD DRAG] NON trascinabile carta Paper-2 in player1-hand - isClickable:false, suit:Paper, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
script.js:4772 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Paper-2 in player1-hand - motivo: isClickable=false, suit=Paper, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
script.js:4768 [CARD DRAG] NON trascinabile carta Paper-7 in player1-hand - isClickable:false, suit:Paper, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
script.js:4772 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Paper-7 in player1-hand - motivo: isClickable=false, suit=Paper, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 5
script.js:4588 [RENDER HAND] Rendering 5 cards to player2-hand, isClickable: true
script.js:4589 [RENDER HAND] Cards: Array(5)
script.js:4593 [BRUSCOLINO RENDER DEBUG] Rendering mano player2-hand, isClickable: true
script.js:4601 [HAND DEBUG] player2-hand: isClickable=true, handLength=5, isOpponentHand=false
script.js:4753 [CARD DRAG] Rendendo trascinabile carta Rock-5 in player2-hand
script.js:4754 [CARD DRAG] Verificando listener su elemento: <div class=​"card rock" data-suit=​"Rock" data-value=​"5" data-card-id=​"Rock-5" draggable=​"true" style=​"width:​ 100px;​ height:​ 147px;​ margin:​ 0px;​ box-sizing:​ border-box;​ border-radius:​ 8px;​ border:​ 1px solid rgba(100, 150, 255, 0.2)​;​ cursor:​ grab;​">​…​</div>​flex
script.js:4758 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Rock-5 in player2-hand
script.js:4766 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
script.js:4753 [CARD DRAG] Rendendo trascinabile carta Paper-10 in player2-hand
script.js:4754 [CARD DRAG] Verificando listener su elemento: <div class=​"card paper" data-suit=​"Paper" data-value=​"10" data-card-id=​"Paper-10" draggable=​"true" style=​"width:​ 100px;​ height:​ 147px;​ margin:​ 0px;​ box-sizing:​ border-box;​ border-radius:​ 8px;​ border:​ 1px solid rgba(100, 150, 255, 0.2)​;​ cursor:​ grab;​">​…​</div>​flex
script.js:4758 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Paper-10 in player2-hand
script.js:4766 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
script.js:4753 [CARD DRAG] Rendendo trascinabile carta Paper-K in player2-hand
script.js:4754 [CARD DRAG] Verificando listener su elemento: <div class=​"card paper" data-suit=​"Paper" data-value=​"K" data-card-id=​"Paper-K" draggable=​"true" style=​"width:​ 100px;​ height:​ 147px;​ margin:​ 0px;​ box-sizing:​ border-box;​ border-radius:​ 8px;​ border:​ 1px solid rgba(100, 150, 255, 0.2)​;​ cursor:​ grab;​">​…​</div>​flex
script.js:4758 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Paper-K in player2-hand
script.js:4766 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
script.js:4753 [CARD DRAG] Rendendo trascinabile carta Paper-A in player2-hand
script.js:4754 [CARD DRAG] Verificando listener su elemento: <div class=​"card paper" data-suit=​"Paper" data-value=​"A" data-card-id=​"Paper-A" draggable=​"true" style=​"width:​ 100px;​ height:​ 147px;​ margin:​ 0px;​ box-sizing:​ border-box;​ border-radius:​ 8px;​ border:​ 1px solid rgba(100, 150, 255, 0.2)​;​ cursor:​ grab;​">​…​</div>​flex
script.js:4758 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Paper-A in player2-hand
script.js:4766 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
script.js:4753 [CARD DRAG] Rendendo trascinabile carta Rock-4 in player2-hand
script.js:4754 [CARD DRAG] Verificando listener su elemento: <div class=​"card rock" data-suit=​"Rock" data-value=​"4" data-card-id=​"Rock-4" draggable=​"true" style=​"width:​ 100px;​ height:​ 147px;​ margin:​ 0px;​ box-sizing:​ border-box;​ border-radius:​ 8px;​ border:​ 1px solid rgba(100, 150, 255, 0.2)​;​ cursor:​ grab;​">​…​</div>​flex
script.js:4758 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Rock-4 in player2-hand
script.js:4766 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
script.js:14166 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
script.js:14167 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
script.js:5111 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
script.js:5124 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
script.js:11264 [BOARD DATA ONLY] Aggiornamento carte tabellone
script.js:11265 [BOARD DATA ONLY] Parametri ricevuti: Object
script.js:11323 [BOARD DATA ONLY] Inizio ciclo aggiornamento carte per 36 posizioni
script.js:11327 [BOARD DATA ONLY] Processando carta in c6: Object
script.js:14169 [DEBUG] updateGameUI AFTER renderBoard
script.js:14170 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
script.js:14171 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
script.js:14175 [DECK COUNTER DEBUG] updateGameUI con state.deckSize: 28 (tipo: number)
script.js:14183 [DECK COUNTER DEBUG] Chiamando renderDeck con validDeckSize: 28
script.js:5736 [DECK COUNTER DEBUG] renderDeck chiamato con remainingCards: 28
script.js:5745 [DECK COUNTER DEBUG] Valori: oldCounterValue=28, newCounterValue=28
script.js:14191 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
script.js:14192 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
script.js:10758 [CONSISTENCY CHECK] 🔍 Verifica consistenza carte tra tabellone e mani...
script.js:10774 [CONSISTENCY CHECK] 📋 Carte sul tabellone (1): Array(1)
script.js:10783 [CONSISTENCY CHECK] 👤 Controllo mano di player1 (white): 5 carte
script.js:10784 [CONSISTENCY CHECK] 👤 Carte in mano: Array(5)
script.js:10783 [CONSISTENCY CHECK] 👤 Controllo mano di player2 (black): 5 carte
script.js:10784 [CONSISTENCY CHECK] 👤 Carte in mano: Array(5)
script.js:10831 [CONSISTENCY CHECK] ✅ Nessuna inconsistenza rilevata
script.js:10835 [CONSISTENCY FINAL] Verifica finale consistenza dopo elaborazione...
script.js:10837 [CONSISTENCY FINAL] ✅ Stato finale consistente
script.js:12439 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
multiplayer.js:1537 [TURN TIMER DEBUG] === ANALISI CONTROLLO TURNO ===
multiplayer.js:1538 [TURN TIMER DEBUG] gameState.currentPlayerId: BZ0EG04KJTrrcpBzAAAR
multiplayer.js:1539 [TURN TIMER DEBUG] gameState.myPlayerId: BZ0EG04KJTrrcpBzAAAR
multiplayer.js:1540 [TURN TIMER DEBUG] window.myPlayerId: BZ0EG04KJTrrcpBzAAAR
multiplayer.js:1541 [TURN TIMER DEBUG] window.socket?.id: 6x5D7qVor5EIyN4yAAAV
multiplayer.js:1547 [TURN TIMER DEBUG] mySocketId: 6x5D7qVor5EIyN4yAAAV
multiplayer.js:1548 [TURN TIMER DEBUG] isMyTurn (CORRETTO): true
multiplayer.js:1549 [TURN TIMER DEBUG] === FINE ANALISI ===
multiplayer.js:1726 [GAMESTATE] Rating giocatore 2 aggiornato: 1000
psn-unified.js:2317 [PSN] Controllo periodico: Ripristino notazione esistente
psn-unified.js:1410 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
psn-unified.js:1425 [PSN] MULTIPLAYER: Incremento singola carta bianco ignorato (possibile sincronizzazione)
script.js:2092 [VISIBILITY] Stato di visibilità cambiato: hidden
script.js:2104 [VISIBILITY] Notificato al server: browser minimizzato
script.js:2373 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
script.js:2092 [VISIBILITY] Stato di visibilità cambiato: visible
script.js:2128 [VISIBILITY] Notificato al server: browser tornato visibile
script.js:2189 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
script.js:2192 [VISIBILITY] Eseguo pulizia cover cards bloccate...
script.js:2205 [VISIBILITY] Nessuna cover card bloccata trovata
script.js:2242 [VISIBILITY] NON riavvio animazione distribuzione carte, mostro stato attuale
script.js:2261 [VISIBILITY DEBUG] currentGameState presente: true
script.js:2262 [VISIBILITY DEBUG] renderGameState definito: true
script.js:2263 [VISIBILITY DEBUG] isMultiplayerGame: true
script.js:2265 [VISIBILITY DEBUG] currentGameState.mode: undefined
script.js:2272 [VISIBILITY] Partita multiplayer rilevata, skip renderGameState per evitare corruzione stato
player-names-protection.js:141 [NAMES PROTECTION] Protezione disattivata - nomi presenti
